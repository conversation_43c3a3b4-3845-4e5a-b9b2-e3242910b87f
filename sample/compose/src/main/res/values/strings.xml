<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<resources>

    <string-array name="abbreviated_months" translatable="false">
        <item>@string/january_abbreviated</item>
        <item>@string/february_abbreviated</item>
        <item>@string/march_abbreviated</item>
        <item>@string/april_abbreviated</item>
        <item>@string/may_abbreviated</item>
        <item>@string/june_abbreviated</item>
        <item>@string/july_abbreviated</item>
        <item>@string/august_abbreviated</item>
        <item>@string/september_abbreviated</item>
        <item>@string/october_abbreviated</item>
        <item>@string/november_abbreviated</item>
        <item>@string/december_abbreviated</item>
    </string-array>
    <string-array name="abbreviated_months_shortest" translatable="false">
        <item>@string/january_abbreviated_shortest</item>
        <item>@string/february_abbreviated_shortest</item>
        <item>@string/march_abbreviated_shortest</item>
        <item>@string/april_abbreviated_shortest</item>
        <item>@string/may_abbreviated_shortest</item>
        <item>@string/june_abbreviated_shortest</item>
        <item>@string/july_abbreviated_shortest</item>
        <item>@string/august_abbreviated_shortest</item>
        <item>@string/september_abbreviated_shortest</item>
        <item>@string/october_abbreviated_shortest</item>
        <item>@string/november_abbreviated_shortest</item>
        <item>@string/december_abbreviated_shortest</item>
    </string-array>

    <string name="january_abbreviated">Jan</string>
    <string name="february_abbreviated">Feb</string>
    <string name="march_abbreviated">Mar</string>
    <string name="april_abbreviated">Apr</string>
    <string name="may_abbreviated">May</string>
    <string name="june_abbreviated">Jun</string>
    <string name="july_abbreviated">Jul</string>
    <string name="august_abbreviated">Aug</string>
    <string name="september_abbreviated">Sep</string>
    <string name="october_abbreviated">Oct</string>
    <string name="november_abbreviated">Nov</string>
    <string name="december_abbreviated">Dec</string>

    <string name="january_abbreviated_shortest">J</string>
    <string name="february_abbreviated_shortest">F</string>
    <string name="march_abbreviated_shortest">M</string>
    <string name="april_abbreviated_shortest">A</string>
    <string name="may_abbreviated_shortest">M</string>
    <string name="june_abbreviated_shortest">J</string>
    <string name="july_abbreviated_shortest">J</string>
    <string name="august_abbreviated_shortest">A</string>
    <string name="september_abbreviated_shortest">S</string>
    <string name="october_abbreviated_shortest">O</string>
    <string name="november_abbreviated_shortest">N</string>
    <string name="december_abbreviated_shortest">D</string>


    <string name="chart_daily_target" translatable="false">Daily target</string>

    <string name="chart_comparison_compare" translatable="false">Compare</string>
    <string name="chart_comparison_hide_comparison" translatable="false">Hide comparison</string>

    <string name="chart_granularity_daily_abbreviation" translatable="false">D</string>
    <string name="chart_granularity_weekly_abbreviation" translatable="false">W</string>
    <string name="chart_granularity_seven_days_abbreviation" translatable="false">7D</string>
    <string name="chart_granularity_monthly_abbreviation" translatable="false">M</string>
    <string name="chart_granularity_thirty_days_abbreviation" translatable="false">30D</string>
    <string name="chart_granularity_six_weeks_abbreviation" translatable="false">6W</string>
    <string name="chart_granularity_six_months_abbreviation" translatable="false">6M</string>
    <string name="chart_granularity_yearly_abbreviation" translatable="false">Y</string>
    <string name="chart_granularity_eight_years_abbreviation" translatable="false">8Y</string>
    <string name="chart_granularity_sixty_day_days_abbreviation" translatable="false">60D</string>
    <string name="chart_granularity_three_hundred_sixty_five_days_abbreviation" translatable="false">365D</string>
    <string name="chart_granularity_one_hundred_eight_days_abbreviation" translatable="false">180D</string>




    <string name="chart_granularity_seven_days" translatable="false">Last 7 days</string>
    <string name="chart_granularity_thirty_days" translatable="false">Last 30 days</string>
    <string name="chart_granularity_six_weeks" translatable="false">6 weeks</string>
    <string name="chart_granularity_six_months" translatable="false">6 months</string>
    <string name="chart_granularity_eight_years" translatable="false">8 years</string>

    <string name="chart_granularity_daily_interval" translatable="false">Daily interval</string>
    <string name="chart_granularity_weekly_interval" translatable="false">Weekly interval</string>
    <string name="chart_granularity_monthly_interval" translatable="false">Monthly interval</string>
    <string name="chart_granularity_yearly_interval" translatable="false">Yearly interval</string>

    <string name="chart_granularity_more" translatable="false">More</string>
    <string name="chart_granularity_time_range_title" translatable="false">Time range</string>
    <string name="chart_granularity_time_range_desc" translatable="false">Select the time window of your analysis</string>


    <string name="chart_value_total" translatable="false">Total</string>
    <string name="chart_value_total_co2e_saved" translatable="false">Total CO₂e saved</string>
    <string name="chart_value_daily_avg" translatable="false">Daily avg.</string>
    <string name="chart_value_range" translatable="false">Range</string>
    <string name="chart_value_min_daytime_hr" translatable="false">Min. daytime HR</string>
    <string name="chart_value_avg_min_daytime_hr" translatable="false">Avg. min. daytime HR</string>
    <string name="chart_value_min_sleep_hr" translatable="false">Min. sleep HR</string>
    <string name="chart_value_avg_sleep_hr" translatable="false">Avg. min. sleep HR</string>
    <string name="chart_value_resting_hr" translatable="false">Resting HR</string>
    <string name="chart_value_avg_resting_hr" translatable="false">Avg. Resting HR</string>
    <string name="chart_value_active" translatable="false">Active</string>
    <string name="chart_value_avg" translatable="false">Avg.</string>

    <string name="chart_content_sleep" translatable="false">Sleep</string>
    <string name="chart_content_steps" translatable="false">Steps</string>
    <string name="chart_content_calories" translatable="false">Calories</string>
    <string name="chart_content_duration" translatable="false">Duration</string>
    <string name="chart_content_ascent" translatable="false">Ascent</string>
    <string name="chart_content_heart_rate" translatable="false">Heart Rate</string>
    <string name="chart_content_minimum_heart_rate" translatable="false">Min. DayTime HR</string>
    <string name="chart_content_sleep_minimum_heart_rate" translatable="false">Min. sleep HR</string>
    <string name="chart_content_resting_heart_rate" translatable="false">Resting HR</string>
    <string name="chart_content_resources" translatable="false">Resources</string>
    <string name="chart_content_hrv" translatable="false">Hrv</string>
    <string name="chart_content_vo2max" translatable="false">VO2Max</string>
    <string name="chart_connect_commute" translatable="false">Commute</string>
    <string name="sleep_heart_rate_compare">Heart rate</string>
    <string name="sleep_heart_rate_hide_comparison">Hide HR</string>

    <string name="km">km</string>
    <string name="mile">mi</string>
    <string name="km_h">km/h</string>
    <string name="m_min">m/min</string>
    <string name="mph">mph</string>
    <string name="ft_min">ft/min</string>
    <string name="per_km">/km</string>
    <string name="per_mi">/mi</string>
    <string name="per_100_m">/100m</string>
    <string name="per_100_yard">/100yd</string>
    <string name="seconds">s</string>
    <string name="minute">min</string>
    <string name="hour">h</string>
    <string name="hours">Hours</string>
    <string name="days">days</string>
    <string name="meters">m</string>
    <string name="feet">ft</string>
    <string name="per_minute">/min</string>
    <string name="minute_km">min/km</string>
    <string name="pounds">lbs</string>
    <string name="kilograms">kg</string>
    <string name="bpm">bpm</string>
    <string name="kcal">kcal</string>
    <string name="watt">W</string>
    <string name="vo2maxUnit">ml/kg/min</string>
    <string name="bar">bar</string>
    <string name="psi">psi</string>
    <string name="sec">sec</string>
    <string name="liters_per_minute">l/min</string>
    <string name="cubic_feet_per_minute">ft³/min</string>
    <string name="cubic_meter_per_second">m³/s</string>
    <string name="kilometers">Kilometers</string>
    <string name="miles">Miles</string>
    <string name="knots">knots</string>
    <string name="nautical_mile">nmi</string>
    <string name="meters_long">meters</string>
    <string name="feet_long">feet</string>
    <string name="ms">ms</string>
    <string name="liters">l</string>
    <string name="cubic_meter">m³</string>
    <string name="cm">cm</string>
    <string name="m_s">m/s</string>
    <string name="inch">inch</string>
    <string name="ft_s">ft/s</string>
    <string name="grams">grams</string>
    <string name="ounce">ounce</string>
    <string name="round">/round</string>
    <string name="min_sec">min\'sec</string>
</resources>
