package com.patrykandpatrick.vico.sample.stt.chart

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.core.common.Defaults
import com.patrykandpatrick.vico.core.common.Fill
import com.patrykandpatrick.vico.core.common.Insets
import com.patrykandpatrick.vico.core.common.shape.Shape

/**
 * 创建并记住一个带有顶部倒三角形装饰的LineComponent
 * 
 * @param fill 线条填充色
 * @param thickness 线条粗细
 * @param shape 线条形状
 * @param margins 边距
 * @param strokeFill 描边填充色
 * @param strokeThickness 描边粗细
 * @param triangleSize 三角形大小
 * @param triangleFill 三角形填充色，null时使用线条颜色
 */
@Composable
fun rememberLineComponentWithTriangle(
    fill: Fill = Fill.Black,
    thickness: Dp = Defaults.LINE_COMPONENT_THICKNESS_DP.dp,
    shape: Shape = Shape.Rectangle,
    margins: Insets = Insets.Zero,
    strokeFill: Fill = Fill.Transparent,
    strokeThickness: Dp = 0.dp,
    triangleSize: Dp = 8.dp,
    triangleFill: Fill? = null,
): LineComponentWithTriangle = remember(
    fill, thickness, shape, margins, strokeFill, strokeThickness, triangleSize, triangleFill
) {
    LineComponentWithTriangle(
        fill = fill,
        thicknessDp = thickness.value,
        shape = shape,
        margins = margins,
        strokeFill = strokeFill,
        strokeThicknessDp = strokeThickness.value,
        triangleSize = triangleSize.value,
        triangleFill = triangleFill
    )
} 