package com.patrykandpatrick.vico.sample.stt.chart

import com.patrykandpatrick.vico.core.cartesian.CartesianDrawingContext
import com.patrykandpatrick.vico.core.cartesian.marker.CartesianMarker
import com.patrykandpatrick.vico.core.common.component.LineComponent

internal class HighlightMarker(
    private val markerLine: LineComponent,
    var drawOverLayers: Boolean,
) : CartesianMarker {
    override fun drawOverLayers(
        context: CartesianDrawingContext,
        targets: List<CartesianMarker.Target>,
    ) {
        if (!drawOverLayers) return

        val x = targets.firstOrNull()?.canvasX ?: return
        markerLine.drawVertical(
            context = context,
            x = x,
            top = 0.0F,
            bottom = context.layerBounds.bottom,
        )
    }
}
