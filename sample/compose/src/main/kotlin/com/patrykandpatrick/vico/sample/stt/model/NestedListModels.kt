package com.patrykandpatrick.vico.sample.stt.model

import androidx.annotation.StringRes
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

data class NestedListSubItem(
    @StringRes val prefixRes: Int?,
    @StringRes val contentRes: Int
)

data class NestedListItem(
    val number: Int?,
    @StringRes val titleRes: Int?,
    @StringRes val descriptionRes: Int?,
    val subItems: ImmutableList<NestedListSubItem> = persistentListOf()
)
