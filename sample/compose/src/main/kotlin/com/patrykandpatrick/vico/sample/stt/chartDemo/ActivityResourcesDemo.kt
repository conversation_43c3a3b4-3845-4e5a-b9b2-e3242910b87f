package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf

@Composable
fun ActivityResourcesDemo(modifier: Modifier) {
    // 创建活动状态资源图表数据
    val chartData = remember {
        // 基于用户提供的数据结构创建图表
        val axisRange = ChartData.AxisRange(
            minX = 2.917104E7,
            maxX = 2.917247E7,
            minY = 0.0,
            maxY = 120.0
        )

        // 系列1: 第一个活动状态（深蓝色）
        val series1 = ChartData.Series(
            chartType = ChartType.BAR,
            color = -2565155, // 用户提供的颜色
            axisRange = axisRange,
            entries = persistentListOf(
                ChartData.Entry(29171250, 52.999996)
            ),
            value = buildAnnotatedString { append("48% - 中等") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 系列2: 第二个活动状态（绿色）
        val series2 = ChartData.Series(
            chartType = ChartType.BAR,
            color = -6495913, // 用户提供的颜色
            axisRange = axisRange,
            entries = persistentListOf(
                ChartData.Entry(29171280, 54.000004),
                ChartData.Entry(29171310, 60.000004),
                ChartData.Entry(29171340, 62.0),
                ChartData.Entry(29171370, 66.0),
                ChartData.Entry(29171400, 70.0),
                ChartData.Entry(29171430, 73.0),
                ChartData.Entry(29171460, 75.0),
                ChartData.Entry(29171490, 77.0)
            ),
            value = buildAnnotatedString { append("48% - 中等") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 系列3: 第三个活动状态（紫色）
        val series3 = ChartData.Series(
            chartType = ChartType.BAR,
            color = -10045697, // 用户提供的颜色
            axisRange = axisRange,
            entries = persistentListOf(
                ChartData.Entry(29171520, 76.0)
            ),
            value = buildAnnotatedString { append("48% - 中等") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 系列4: 第四个活动状态（重复深蓝色）
        val series4 = ChartData.Series(
            chartType = ChartType.BAR,
            color = -2565155,
            axisRange = axisRange,
            entries = persistentListOf(
                ChartData.Entry(29171550, 75.0)
            ),
            value = buildAnnotatedString { append("48% - 中等") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 系列5: 第五个活动状态（重复紫色）
        val series5 = ChartData.Series(
            chartType = ChartType.BAR,
            color = -10045697,
            axisRange = axisRange,
            entries = persistentListOf(
                ChartData.Entry(29171580, 73.0)
            ),
            value = buildAnnotatedString { append("48% - 中等") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 系列6: 第六个活动状态（黄色）
        val series6 = ChartData.Series(
            chartType = ChartType.BAR,
            color = -33733,
            axisRange = axisRange,
            entries = persistentListOf(
                ChartData.Entry(29171610, 71.0),
                ChartData.Entry(29171640, 67.0),
                ChartData.Entry(29171670, 64.0),
                ChartData.Entry(29171700, 61.0)
            ),
            value = buildAnnotatedString { append("48% - 中等") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 系列7: 第七个活动状态（长时间数据系列）
        val series7 = ChartData.Series(
            chartType = ChartType.BAR,
            color = -2565155,
            axisRange = axisRange,
            entries = persistentListOf(
                ChartData.Entry(29171730, 58.999996),
                ChartData.Entry(29171760, 58.999996),
                ChartData.Entry(29171790, 58.0),
                ChartData.Entry(29171820, 58.0),
                ChartData.Entry(29171850, 57.0),
                ChartData.Entry(29171880, 57.0),
                ChartData.Entry(29171910, 56.0),
                ChartData.Entry(29171940, 56.0),
                ChartData.Entry(29171970, 55.0),
                ChartData.Entry(29172000, 55.0),
                ChartData.Entry(29172030, 55.0),
                ChartData.Entry(29172060, 54.000004),
                ChartData.Entry(29172090, 52.999996),
                ChartData.Entry(29172120, 52.999996)
            ),
            value = buildAnnotatedString { append("48% - 中等") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 系列8: 第八个活动状态
        val series8 = ChartData.Series(
            chartType = ChartType.BAR,
            color = -10045697,
            axisRange = axisRange,
            entries = persistentListOf(
                ChartData.Entry(29172150, 52.0)
            ),
            value = buildAnnotatedString { append("48% - 中等") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 系列9: 第九个活动状态
        val series9 = ChartData.Series(
            chartType = ChartType.BAR,
            color = -2565155,
            axisRange = axisRange,
            entries = persistentListOf(
                ChartData.Entry(29172180, 52.0)
            ),
            value = buildAnnotatedString { append("48% - 中等") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 系列10: 第十个活动状态
        val series10 = ChartData.Series(
            chartType = ChartType.BAR,
            color = -10045697,
            axisRange = axisRange,
            entries = persistentListOf(
                ChartData.Entry(29172210, 52.0),
                ChartData.Entry(29172240, 51.0),
                ChartData.Entry(29172270, 50.0),
                ChartData.Entry(29172300, 48.0)
            ),
            value = buildAnnotatedString { append("48% - 中等") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 构建完整的ChartData对象
        ChartData(
            chartContent = ChartContent.RESOURCES,
            chartGranularity = ChartGranularity.DAILY,
            series = persistentListOf(
                series1, series2, series3, series4, series5,
                series6, series7, series8, series9, series10
            ),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            colorIndicator = null,
            selectEntryX = null
        )
    }

    // 记录当前选中的项
    var selectedX by remember { mutableStateOf<Long?>(null) }

    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "活动状态资源图表",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = "展示一天中不同时段的活动状态分布（日粒度，堆叠模式）",
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
        )

        // 显示当前值数据（模拟用户提供的currentValues）
        Column(modifier = Modifier.padding(16.dp)) {
            Text(
                text = "当前状态统计：",
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(bottom = 8.dp)
            )
            Text("🟢 活跃: 3 小时 30 分", style = MaterialTheme.typography.bodyMedium)
            Text("🔴 不活跃: 8 小时 30 分", style = MaterialTheme.typography.bodyMedium)
            Text("🟡 有压力: 2 小时 0 分", style = MaterialTheme.typography.bodyMedium)
            Text("🔵 正在恢复: 4 小时 0 分", style = MaterialTheme.typography.bodyMedium)
        }

        // 显示选中的数据点信息
        selectedX?.let { xValue ->
            val selectedSeries = chartData.series.mapNotNull { series ->
                series.entries.find { it.x == xValue }?.let { entry ->
                    "系列${chartData.series.indexOf(series) + 1}: ${entry.y}"
                }
            }

            if (selectedSeries.isNotEmpty()) {
                Text(
                    text = "选中时间点 $xValue 的数据:\n${selectedSeries.joinToString("\n")}",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
                )
            }
        }

        // 渲染图表
        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedX = x },
            onNoEntrySelected = { selectedX = null },
            modifier = Modifier
                .fillMaxWidth()
                .height(350.dp)
                .padding(16.dp)
        )
    }
}
