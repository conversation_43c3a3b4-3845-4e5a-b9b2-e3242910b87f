package com.patrykandpatrick.vico.sample.stt.chart

import android.graphics.Path
import android.graphics.Paint
import com.patrykandpatrick.vico.core.cartesian.CartesianDrawingContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.decoration.Decoration
import com.patrykandpatrick.vico.core.common.component.ShapeComponent
import com.patrykandpatrick.vico.sample.stt.model.ChartData

internal class BackgroundRegionDecoration(
  private val entries: List<ChartData.BackgroundRegionEntry>,
  private val backgroundComponent: ShapeComponent,
  private val color: Int,
  private val verticalAxisPosition: Axis.Position.Vertical?,
) : Decoration {
    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
        this.color = <EMAIL>
    }

    override fun drawUnderLayers(context: CartesianDrawingContext) {
        with(context) {
            if (entries.isEmpty()) return

            val yRange = ranges.getYRange(verticalAxisPosition)
            val xLength = ranges.maxX - ranges.minX

            if (entries.size < 2) return

            val path = Path()

            val firstEntry = entries.first()
            var x = layerBounds.left + ((firstEntry.x - ranges.minX) / xLength).toFloat() * layerBounds.width()
            var y = layerBounds.bottom - ((firstEntry.startY - yRange.minY) / yRange.length).toFloat() * layerBounds.height()
            path.moveTo(x, y)

            for (i in 1 until entries.size) {
                val entry = entries[i]
                x = layerBounds.left + ((entry.x - ranges.minX) / xLength).toFloat() * layerBounds.width()
                y = layerBounds.bottom - ((entry.startY - yRange.minY) / yRange.length).toFloat() * layerBounds.height()
                path.lineTo(x, y)
            }

            val lastEntry = entries.last()
            x = layerBounds.left + ((lastEntry.x - ranges.minX) / xLength).toFloat() * layerBounds.width()
            y = layerBounds.bottom - ((lastEntry.endY - yRange.minY) / yRange.length).toFloat() * layerBounds.height()
            path.lineTo(x, y)

            for (i in entries.size - 2 downTo 0) {
                val entry = entries[i]
                x = layerBounds.left + ((entry.x - ranges.minX) / xLength).toFloat() * layerBounds.width()
                y = layerBounds.bottom - ((entry.endY - yRange.minY) / yRange.length).toFloat() * layerBounds.height()
                path.lineTo(x, y)
            }

            path.close()

            canvas.drawPath(path, paint)
        }
    }

    override fun equals(other: Any?): Boolean =
        this === other ||
            other is BackgroundRegionDecoration &&
                entries == other.entries &&
                backgroundComponent == other.backgroundComponent &&
                verticalAxisPosition == other.verticalAxisPosition

    override fun hashCode(): Int {
        var result = entries.hashCode()
        result = 31 * result + backgroundComponent.hashCode()
        result = 31 * result + (verticalAxisPosition?.hashCode() ?: 0)
        return result
    }
}
