package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@Composable
fun StepsWeeklyDemo(modifier: Modifier) {
    // 日期格式化工具
    val dateFormatter = remember { SimpleDateFormat("MM/dd", Locale.getDefault()) }

    // 格式化具体日期
    fun formatDate(timestamp: Long): String {
        // 数据X轴时间戳是使用基于2024年的日期序号
        // 创建一个2024年1月1日的基准日期
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 1, 0, 0, 0)
        calendar.set(Calendar.MILLISECOND, 0)

        // 计算目标日期 = 基准日期 + (timestamp - 20000) 天
        val daysToAdd = timestamp - 20000
        calendar.add(Calendar.DAY_OF_YEAR, daysToAdd.toInt())

        return dateFormatter.format(calendar.time)
    }

    // 创建步数统计图表数据
    val chartData = remember {
        ChartData(
            chartContent = ChartContent.STEPS,
            chartGranularity = ChartGranularity.SIX_MONTHS,
            series = persistentListOf(
                ChartData.Series(
                    chartType = ChartType.BAR,
                    color = -10045697, // 蓝绿色
                    axisRange = ChartData.AxisRange(
                        minX = 19905.0,
                        maxX = 20087.0,
                        minY = 0.0,
                        maxY = 12000.0
                    ),
                    entries = persistentListOf(
                        ChartData.Entry(x = 19905, y = 2201.0),
                        ChartData.Entry(x = 19912, y = 3921.0),
                        ChartData.Entry(x = 19919, y = 3112.6),
                        ChartData.Entry(x = 19926, y = 2937.8572),
                        ChartData.Entry(x = 19933, y = 2877.5),
                        ChartData.Entry(x = 19968, y = 2934.25),
                        ChartData.Entry(x = 19975, y = 958.0),
                        ChartData.Entry(x = 19982, y = 2165.5),
                        ChartData.Entry(x = 19989, y = 2014.0),
                        ChartData.Entry(x = 20003, y = 960.75),
                        ChartData.Entry(x = 20010, y = 354.0),
                        ChartData.Entry(x = 20059, y = 1147.3334),
                      ChartData.Entry(x = 20066, y = 1187.3334),
                      ChartData.Entry(x = 20073, y = 1187.3334),


                    ),
                    value = buildAnnotatedString { append("平均: 2452步/天") },
                    candlestickEntries = persistentListOf(),
                    lineConfig = LineChartConfig(
                        isSmoothCurve = true,
                        showPoints = false,
                        pointSizeDP = null,
                        isPointFilled = true,
                        showAreaFill = true,
                        areaAlpha = null,
                        thicknessDP = null
                    ),
                    backgroundRegion = null,
                    groupStackBarEntries = persistentListOf(),
                    groupStackBarStyle = null,
                    average = null
                )
            ),
            highlightEnabled = true,
            goal = 10000,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            colorIndicator = null,
            selectEntryX = null
        )
    }

    var selectedEntryIndex by remember { mutableStateOf<Int?>(null) }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 标题区域
        Text(
            text = "每周步数统计",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Text(
            text = "6个月期间每周平均步数变化",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // 统计信息区域
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "最高记录",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "3,921步",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = formatDate(19912),
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "平均步数",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "2,452步",
                    style = MaterialTheme.typography.bodyLarge
                )
            }

            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "目标达成",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "0/12周",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }

                // 图表区域
        Chart(
            chartData = chartData,
            onEntrySelected = { x ->
                // 根据x值找到对应的条目索引
                selectedEntryIndex = chartData.series.firstOrNull()?.entries?.indexOfFirst { it.x == x }
            },
            onNoEntrySelected = { selectedEntryIndex = null },
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp)
        )

        // 选中条目的详细信息
        selectedEntryIndex?.let { index ->
            val selectedEntry = chartData.series.firstOrNull()?.entries?.getOrNull(index)
            selectedEntry?.let { entry ->
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp)
                ) {
                    Text(
                        text = "选中数据详情",
                        style = MaterialTheme.typography.titleSmall,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "日期",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = formatDate(entry.x.toLong()),
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }

                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "步数",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Text(
                                text = "${entry.y.toDouble().toInt()}步",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.primary
                            )
                        }

                        Column(modifier = Modifier.weight(1f)) {
                            Text(
                                text = "目标完成度",
                                style = MaterialTheme.typography.labelSmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            val percentage = (entry.y.toDouble() / 10000.0 * 100).toInt()
                            Text(
                                text = "${percentage}%",
                                style = MaterialTheme.typography.bodyMedium,
                                color = if (percentage >= 100) {
                                    MaterialTheme.colorScheme.primary
                                } else {
                                    MaterialTheme.colorScheme.error
                                }
                            )
                        }
                    }
                }
            }
        }

        // 数据说明
        Text(
            text = "※ 数据来源：6个月期间的每周步数统计",
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 16.dp)
        )
    }
}
