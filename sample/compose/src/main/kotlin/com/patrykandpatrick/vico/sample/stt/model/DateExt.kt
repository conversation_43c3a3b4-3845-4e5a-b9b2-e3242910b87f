package com.patrykandpatrick.vico.sample.stt.model

import android.os.Build
import androidx.annotation.RequiresApi
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZonedDateTime

internal val LocalDate.epochMonth: Int @RequiresApi(Build.VERSION_CODES.O)
get() = (year - 1970) * 12 + monthValue - 1

@RequiresApi(Build.VERSION_CODES.O)
internal fun firstDayOfEpochMonth(epochMonth: Int): LocalDate {
    val year = 1970 + (epochMonth) / 12
    val month = (epochMonth % 12) + 1
    return LocalDate.of(year, month, 1)
}

internal val LocalDateTime.minutesSinceEpoch: Long @RequiresApi(Build.VERSION_CODES.O)
get() = toEpochSecond(OffsetDateTime.now().offset) / 60L

internal val ZonedDateTime.minutesSinceEpoch: Long @RequiresApi(Build.VERSION_CODES.O)
get() = toEpochSecond() / 60L
