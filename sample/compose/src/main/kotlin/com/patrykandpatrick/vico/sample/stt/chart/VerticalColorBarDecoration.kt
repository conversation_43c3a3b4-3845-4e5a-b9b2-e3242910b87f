package com.patrykandpatrick.vico.sample.stt.chart

import android.graphics.Paint
import android.graphics.Typeface
import com.patrykandpatrick.vico.core.cartesian.CartesianDrawingContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.decoration.Decoration
import com.patrykandpatrick.vico.core.common.Fill
import com.patrykandpatrick.vico.core.common.Insets
import com.patrykandpatrick.vico.core.common.shape.Shape

internal class VerticalColorBarDecoration(
    private val thresholds: List<Number>,
    private val colors: List<Int>,
    private val labelTexts: List<String>,
    private val barThicknessDp: Float,
    private val barShiftXDp: Float,
    private val verticalAxisPosition: Axis.Position.Vertical?,
) : Decoration {

    private val textPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = android.graphics.Color.BLACK
        textAlign = Paint.Align.RIGHT
        textSize = 30f
        typeface = Typeface.DEFAULT
    }

    init {
        require(thresholds.isNotEmpty()) { "Thresholds cannot be empty" }
        require(colors.size == thresholds.size - 1) { "Colors size must be thresholds size - 1" }
        require(labelTexts.isEmpty() || labelTexts.size == thresholds.size) {
            "Label texts size must match thresholds size when not empty"
        }
    }

    override fun drawUnderLayers(context: CartesianDrawingContext) {
        with(context) {
            val yRange = ranges.getYRange(verticalAxisPosition)
            val barThickness = density * barThicknessDp
            val barX = layerBounds.left + density * barShiftXDp

            for (i in colors.indices) {
                val startY = layerBounds.bottom - ((thresholds[i].toDouble() - yRange.minY) / yRange.length).toFloat() * layerBounds.height()
                val endY = layerBounds.bottom - ((thresholds[i + 1].toDouble() - yRange.minY) / yRange.length).toFloat() * layerBounds.height()

                val colorBarComponent = ShapeComponentHelper(
                    fill = Fill(colors[i]),
                    shape = Shape.Rectangle
                )

                colorBarComponent.draw(
                    context = this,
                    left = barX,
                    top = endY,
                    right = barX + barThickness,
                    bottom = startY
                )
            }

            if (labelTexts.isNotEmpty()) {
                for (i in thresholds.indices) {
                    if (i < labelTexts.size) {
                        val y = layerBounds.bottom - ((thresholds[i].toDouble() - yRange.minY) / yRange.length).toFloat() * layerBounds.height()

                        canvas.drawText(
                            labelTexts[i],
                            barX + barThickness + 5 * density,
                            y + textPaint.textSize / 3,
                            textPaint
                        )
                    }
                }
            }
        }
    }

    private class ShapeComponentHelper(
        fill: Fill,
        shape: Shape = Shape.Rectangle,
        margins: Insets = Insets.Zero,
    ) {
        private val component = com.patrykandpatrick.vico.core.common.component.ShapeComponent(
            fill = fill,
            shape = shape,
            margins = margins
        )

        fun draw(context: CartesianDrawingContext, left: Float, top: Float, right: Float, bottom: Float) {
            component.draw(context, left, top, right, bottom)
        }
    }
}


