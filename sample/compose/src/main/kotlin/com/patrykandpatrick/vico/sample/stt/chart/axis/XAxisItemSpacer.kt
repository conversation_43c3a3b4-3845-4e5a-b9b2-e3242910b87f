package com.stt.android.chart.impl.chart.axis

import android.util.Log
import com.patrykandpatrick.vico.core.cartesian.CartesianDrawingContext
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModel
import com.patrykandpatrick.vico.core.cartesian.layer.CartesianLayerDimensions
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.math.abs

internal fun createXAxisItemPlacer(
  chartGranularity: ChartGranularity,
): HorizontalAxis.ItemPlacer = when (chartGranularity) {
    ChartGranularity.DAILY -> HorizontalAxis.ItemPlacer.aligned(spacing = { 36 })
    ChartGranularity.WEEKLY,
    ChartGranularity.SEVEN_DAYS,
    ChartGranularity.YEARLY,
    ChartGranularity.EIGHT_YEARS -> HorizontalAxis.ItemPlacer.aligned()
    ChartGranularity.SIX_MONTHS -> DynamicMonthItemPlacer() // 使用新的动态ItemPlacer
    ChartGranularity.MONTHLY -> OptimizedMonthlyItemPlacer() // 月度模式：从月初开始，每7天一个标签
    ChartGranularity.THIRTY_DAYS -> ThirtyDaysItemPlacer() // 30天模式：从minX开始，每7天一个标签
    ChartGranularity.SIXTY_DAYS,
    ChartGranularity.SIX_WEEKS -> HorizontalAxis.ItemPlacer.aligned(spacing = { 7 })
    ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> HorizontalAxis.ItemPlacer.aligned(spacing = { 91 })
    ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> HorizontalAxis.ItemPlacer.aligned(spacing = {21})
}

private class DynamicMonthItemPlacer : HorizontalAxis.ItemPlacer {

  override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

  override fun getHeightMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getMonthlyLabelPositions(fullXRange)
  }

  override fun getWidthMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
  ): List<Double> = getMonthlyLabelPositions(fullXRange)

  override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    val ranges = context.ranges
    return ranges.minX
  }

  override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    val ranges = context.ranges
    return ranges.maxX
  }

  override fun getLabelValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getMonthlyLabelPositions(fullXRange)
  }

  override fun getLineValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val monthlyLabels = getMonthlyLabelPositions(fullXRange)
    val allTicks = mutableListOf<Double>()
    allTicks.addAll(monthlyLabels)

    for (i in 0 until monthlyLabels.size - 1) {
      val startPos = monthlyLabels[i]
      val endPos = monthlyLabels[i + 1]
      val interval = (endPos - startPos) / 4.0
      for (j in 1..3) {
        allTicks.add(startPos + j * interval)
      }
    }

    if (monthlyLabels.isNotEmpty()) {
      var pos = monthlyLabels.first().toLong() - 7
      while (pos >= fullXRange.start.toLong()) {
        allTicks.add(pos.toDouble())
        pos -= 7
      }
    }

    if (monthlyLabels.isNotEmpty()) {
      var pos = monthlyLabels.last().toLong() + 7
      while (pos <= fullXRange.endInclusive.toLong()) {
        allTicks.add(pos.toDouble())
        pos += 7
      }
    }

    return allTicks.filter { it >= visibleXRange.start && it <= visibleXRange.endInclusive }.sorted().distinct()
  }

  private fun getMonthlyLabelPositions(fullXRange: ClosedFloatingPointRange<Double>): List<Double> {
    val startEpochDay = fullXRange.start.toLong()
    val endEpochDay = fullXRange.endInclusive.toLong()
    val startDate = LocalDate.ofEpochDay(startEpochDay)
    val endDate = LocalDate.ofEpochDay(endEpochDay)

    val labelPositions = mutableListOf<Double>()

    var currentDate = startDate.withDayOfMonth(1)

    if (startDate.dayOfMonth > 1) {
      currentDate = currentDate.plusMonths(1)
    }

    var monthCount = 0
    while (!currentDate.isAfter(endDate) && monthCount < 6) {
      val epochDay = currentDate.toEpochDay().toDouble()

      if (epochDay >= fullXRange.start && epochDay <= fullXRange.endInclusive) {
        labelPositions.add(epochDay)
        monthCount++
      }

      currentDate = currentDate.plusMonths(1)
    }

    if (labelPositions.size < 6) {
      val totalRange = endEpochDay - startEpochDay
      val interval = totalRange / 5.0
      labelPositions.clear()

      for (i in 0..5) {
        val position = startEpochDay + (i * interval).toLong()
        if (position <= endEpochDay) {
          labelPositions.add(position.toDouble())
        }
      }
    }

    return labelPositions.sorted()
  }

  override fun getStartLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float = maxLabelWidth * 0.6f // 给第一个标签留足够的空间

  override fun getEndLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float = maxLabelWidth * 0.6f // 给最后一个标签留足够的空间
}
private class WeeklyWithDailyTicksItemPlacer : HorizontalAxis.ItemPlacer {

  override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

  override fun getHeightMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getWeeklyLabelPositions(fullXRange)
  }

  override fun getWidthMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
  ): List<Double> = getWeeklyLabelPositions(fullXRange)

  override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double? {
    val ranges = context.ranges
    return ranges.minX
  }

  override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double? {
    return null
  }

  override fun getLabelValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getWeeklyLabelPositions(fullXRange)
  }

  override fun getLineValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val weeklyLabels = getWeeklyLabelPositions(fullXRange)
    val allTicks = mutableListOf<Double>()

    // 1. 添加所有标签位置的主刻度线
    allTicks.addAll(weeklyLabels)

    // 2. 在整个范围内，每天添加一个刻度线
    val startDay = fullXRange.start.toLong()
    val endDay = fullXRange.endInclusive.toLong()

    for (day in startDay..endDay) {
      allTicks.add(day.toDouble())
    }

    return allTicks.sorted().distinct()
  }

  private fun getWeeklyLabelPositions(fullXRange: ClosedFloatingPointRange<Double>): List<Double> {
    val startEpochDay = fullXRange.start.toLong()
    val endEpochDay = fullXRange.endInclusive.toLong()

    val labelPositions = mutableListOf<Double>()

    // 从起始日期开始，每隔7天生成一个标签位置
    var currentDay = startEpochDay

    // 找到第一个对齐的7天边界（可选：对齐到周一等）
    // 这里简单地从起始日期开始，每7天一个标签
    while (currentDay <= endEpochDay) {
      labelPositions.add(currentDay.toDouble())
      currentDay += 7
    }

    return labelPositions
  }

  override fun getStartLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float = maxLabelWidth * 0.6f // 给第一个标签留足够的空间

  override fun getEndLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float = maxLabelWidth * 0.6f // 给最后一个标签留足够的空间
}

/**
 * 月度ItemPlacer - 专门用于月度图表显示
 *
 * 月度模式特性：
 * - 总是从月初开始显示标签
 * - 每隔一天展示一个刻度
 * - 每隔7天展示一个label
 * - 适用于按月查看的数据展示
 */
internal class OptimizedMonthlyItemPlacer : HorizontalAxis.ItemPlacer {

  override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

  override fun getHeightMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getOptimizedLabelPositions(fullXRange)
  }

  override fun getWidthMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
  ): List<Double> = getOptimizedLabelPositions(fullXRange)

  override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    val ranges = context.ranges
    return ranges.minX
  }

  override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    val ranges = context.ranges
    return ranges.maxX
  }

  override fun getLabelValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getOptimizedLabelPositions(fullXRange)
  }

  override fun getLineValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val labelPositions = getOptimizedLabelPositions(fullXRange)
    val allTicks = mutableListOf<Double>()

    allTicks.addAll(labelPositions)

    val startDay = fullXRange.start.toLong()
    val endDay = fullXRange.endInclusive.toLong()

    for (day in startDay..endDay) {
      allTicks.add(day.toDouble())
    }

    return allTicks.filter { it >= visibleXRange.start && it <= visibleXRange.endInclusive }
      .sorted()
      .distinct()
  }


  private fun getOptimizedLabelPositions(fullXRange: ClosedFloatingPointRange<Double>): List<Double> {
    val startEpochDay = fullXRange.start.toLong()
    val endEpochDay = fullXRange.endInclusive.toLong()
    val labelPositions = mutableListOf<Double>()

    val midPoint = (startEpochDay + endEpochDay) / 2
    val midDate = LocalDate.ofEpochDay(midPoint)
    val targetMonth = midDate.month
    val targetYear = midDate.year

    val firstDayOfTargetMonth = LocalDate.of(targetYear, targetMonth, 1)
    val monthFirstDay = firstDayOfTargetMonth.toEpochDay()

    var currentDay = monthFirstDay

    while (currentDay <= endEpochDay + 7) {
      if (currentDay >= startEpochDay - 14 && currentDay <= endEpochDay + 7) {
        labelPositions.add(currentDay.toDouble())
      }
      currentDay += 7
    }

    return labelPositions
  }

  override fun getStartLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float {
    return maxLabelWidth / 2f
  }

  override fun getEndLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float {
    return maxLabelWidth / 2f
  }
}
/**
 * 30天模式的ItemPlacer：从minX开始，每7天一个标签，总共显示5个标签
 * 适用于"最近30天"等任意日期范围的场景
 *
 * 核心策略：
 * - 模拟 addExtremeLabelPadding 的行为，确保极端标签可见
 * - 第一个标签：从 ranges.minX 开始
 * - 后续标签：每隔7天显示一个
 * - 标签数量：固定显示5个标签
 * - 通过返回 null 让 Vico 自动处理边距
 */
private class ThirtyDaysItemPlacer : HorizontalAxis.ItemPlacer {

  companion object {
    private const val TAG = "ThirtyDaysItemPlacer"
  }

  override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

  override fun getHeightMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> = getThirtyDaysLabelPositions(fullXRange, context.ranges.minX, context.ranges.maxX)

  override fun getWidthMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
  ): List<Double> = getThirtyDaysLabelPositions(fullXRange, context.ranges.minX, context.ranges.maxX)

  override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double? {
    // 30天模式：返回 null 让 Vico 自动处理第一个标签的边距
    // 这样可以避免第一个标签被隐藏
    Log.d(TAG, "🔍 getFirstLabelValue: 返回 null，让 Vico 自动处理边距, maxLabelWidth=$maxLabelWidth")
    return null
  }

  override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double? {
    // 30天模式：返回 null 让 Vico 自动处理最后一个标签的边距
    // 这样可以避免最后一个标签被隐藏
    Log.d(TAG, "🔍 getLastLabelValue: 返回 null，让 Vico 自动处理边距, maxLabelWidth=$maxLabelWidth")
    return null
  }

  override fun getLabelValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val labelPositions = getThirtyDaysLabelPositions(fullXRange, context.ranges.minX, context.ranges.maxX)
    Log.d(TAG, "🔍 getLabelValues: visibleXRange=$visibleXRange, fullXRange=$fullXRange")
    Log.d(TAG, "🔍 ranges: ${context.ranges.minX} - ${context.ranges.maxX}")
    Log.d(TAG, "🔍 ranges日期: ${LocalDate.ofEpochDay(context.ranges.minX.toLong())} - ${LocalDate.ofEpochDay(context.ranges.maxX.toLong())}")
    Log.d(TAG, "🔍 返回的标签位置: $labelPositions")
    Log.d(TAG, "🔍 对应的日期: ${labelPositions.map { LocalDate.ofEpochDay(it.toLong()) }}")
    return labelPositions
  }

  override fun getLineValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val startEpochDay = fullXRange.start.toLong()
    val endEpochDay = fullXRange.endInclusive.toLong()
    val allTicks = mutableListOf<Double>()

    // 生成每日刻度线
    for (day in startEpochDay..endEpochDay) {
      allTicks.add(day.toDouble())
    }

    return allTicks.filter { it >= visibleXRange.start && it <= visibleXRange.endInclusive }.distinct()
  }

  override fun getStartLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float {
    // 30天模式：为第一个标签提供足够的边距，确保从 minX 开始的标签完整显示
    // 使用更大的边距来确保标签不被隐藏
    val margin = maxLabelWidth * 0.8f
    Log.d(TAG, "🔍 getStartLayerMargin: maxLabelWidth=$maxLabelWidth, margin=$margin")
    return margin
  }

  override fun getEndLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float {
    // 30天模式：为最后一个标签提供足够的边距，确保第5个标签完整显示
    // 使用更大的边距来确保标签不被隐藏
    val margin = maxLabelWidth * 0.8f
    Log.d(TAG, "🔍 getEndLayerMargin: maxLabelWidth=$maxLabelWidth, margin=$margin")
    return margin
  }

  /**
   * 30天模式：从真实的minX开始，每7天一个标签，固定显示5个标签
   *
   * 新策略：
   * - 简化逻辑，固定生成5个标签
   * - 依赖 Vico 的边距机制来确保标签可见性
   * - 从真实的数据起始点开始，不考虑 fullXRange 的 padding
   */
  private fun getThirtyDaysLabelPositions(
    fullXRange: ClosedFloatingPointRange<Double>,
    actualMinX: Double? = null,
    actualMaxX: Double? = null
  ): List<Double> {
    val labelPositions = mutableListOf<Double>()

    // 使用真实的 minX 作为起始点
    val realStartDay = actualMinX?.toLong() ?: fullXRange.start.toLong()

    // 从真实的 minX 开始，每隔7天生成一个标签，固定5个标签
    var currentDay = realStartDay

    for (i in 0 until 5) {
      labelPositions.add(currentDay.toDouble())
      currentDay += 7
    }

    Log.d(TAG, "🔍 fullXRange: ${fullXRange.start} - ${fullXRange.endInclusive}")
    Log.d(TAG, "🔍 actualMinX: $actualMinX, actualMaxX: $actualMaxX")
    Log.d(TAG, "🔍 realStartDay: $realStartDay (${LocalDate.ofEpochDay(realStartDay)})")
    Log.d(TAG, "🔍 生成的标签位置: $labelPositions")
    Log.d(TAG, "🔍 对应的日期: ${labelPositions.map { LocalDate.ofEpochDay(it.toLong()) }}")

    return labelPositions
  }
}

internal fun calculateXAxisStep(
    chartGranularity: ChartGranularity,
): (CartesianChartModel) -> Double = {
    when (chartGranularity) {
        ChartGranularity.DAILY -> 10.0
        ChartGranularity.SIX_MONTHS -> 7.0
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> 1.0
    }
}
