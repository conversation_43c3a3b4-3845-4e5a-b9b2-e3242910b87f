package com.stt.android.chart.impl.chart.axis

import android.util.Log
import com.patrykandpatrick.vico.core.cartesian.CartesianDrawingContext
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModel
import com.patrykandpatrick.vico.core.cartesian.layer.CartesianLayerDimensions
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.math.abs

internal fun createXAxisItemPlacer(
  chartGranularity: ChartGranularity,
): HorizontalAxis.ItemPlacer = when (chartGranularity) {
    ChartGranularity.DAILY -> HorizontalAxis.ItemPlacer.aligned(spacing = { 36 })
    ChartGranularity.WEEKLY,
    ChartGranularity.SEVEN_DAYS,
    ChartGranularity.YEARLY,
    ChartGranularity.EIGHT_YEARS -> HorizontalAxis.ItemPlacer.aligned()
    ChartGranularity.SIX_MONTHS -> DynamicMonthItemPlacer() // 使用新的动态ItemPlacer
    ChartGranularity.MONTHLY -> OptimizedMonthlyItemPlacer() // 月度模式：从月初开始，每7天一个标签
    ChartGranularity.THIRTY_DAYS -> FixedThirtyDaysItemPlacer() // 30天模式：基于OptimizedMonthlyItemPlacer的成功模式，但从minX开始
    ChartGranularity.SIXTY_DAYS,
    ChartGranularity.SIX_WEEKS -> HorizontalAxis.ItemPlacer.aligned(spacing = { 7 })
    ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> HorizontalAxis.ItemPlacer.aligned(spacing = { 91 })
    ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> HorizontalAxis.ItemPlacer.aligned(spacing = {21})
}

private class DynamicMonthItemPlacer : HorizontalAxis.ItemPlacer {

  override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

  override fun getHeightMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getMonthlyLabelPositions(fullXRange)
  }

  override fun getWidthMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
  ): List<Double> = getMonthlyLabelPositions(fullXRange)

  override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    val ranges = context.ranges
    return ranges.minX
  }

  override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    val ranges = context.ranges
    return ranges.maxX
  }

  override fun getLabelValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getMonthlyLabelPositions(fullXRange)
  }

  override fun getLineValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val monthlyLabels = getMonthlyLabelPositions(fullXRange)
    val allTicks = mutableListOf<Double>()
    allTicks.addAll(monthlyLabels)

    for (i in 0 until monthlyLabels.size - 1) {
      val startPos = monthlyLabels[i]
      val endPos = monthlyLabels[i + 1]
      val interval = (endPos - startPos) / 4.0
      for (j in 1..3) {
        allTicks.add(startPos + j * interval)
      }
    }

    if (monthlyLabels.isNotEmpty()) {
      var pos = monthlyLabels.first().toLong() - 7
      while (pos >= fullXRange.start.toLong()) {
        allTicks.add(pos.toDouble())
        pos -= 7
      }
    }

    if (monthlyLabels.isNotEmpty()) {
      var pos = monthlyLabels.last().toLong() + 7
      while (pos <= fullXRange.endInclusive.toLong()) {
        allTicks.add(pos.toDouble())
        pos += 7
      }
    }

    return allTicks.filter { it >= visibleXRange.start && it <= visibleXRange.endInclusive }.sorted().distinct()
  }

  private fun getMonthlyLabelPositions(fullXRange: ClosedFloatingPointRange<Double>): List<Double> {
    val startEpochDay = fullXRange.start.toLong()
    val endEpochDay = fullXRange.endInclusive.toLong()
    val startDate = LocalDate.ofEpochDay(startEpochDay)
    val endDate = LocalDate.ofEpochDay(endEpochDay)

    val labelPositions = mutableListOf<Double>()

    var currentDate = startDate.withDayOfMonth(1)

    if (startDate.dayOfMonth > 1) {
      currentDate = currentDate.plusMonths(1)
    }

    var monthCount = 0
    while (!currentDate.isAfter(endDate) && monthCount < 6) {
      val epochDay = currentDate.toEpochDay().toDouble()

      if (epochDay >= fullXRange.start && epochDay <= fullXRange.endInclusive) {
        labelPositions.add(epochDay)
        monthCount++
      }

      currentDate = currentDate.plusMonths(1)
    }

    if (labelPositions.size < 6) {
      val totalRange = endEpochDay - startEpochDay
      val interval = totalRange / 5.0
      labelPositions.clear()

      for (i in 0..5) {
        val position = startEpochDay + (i * interval).toLong()
        if (position <= endEpochDay) {
          labelPositions.add(position.toDouble())
        }
      }
    }

    return labelPositions.sorted()
  }

  override fun getStartLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float = maxLabelWidth * 0.6f // 给第一个标签留足够的空间

  override fun getEndLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float = maxLabelWidth * 0.6f // 给最后一个标签留足够的空间
}
private class WeeklyWithDailyTicksItemPlacer : HorizontalAxis.ItemPlacer {

  override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

  override fun getHeightMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getWeeklyLabelPositions(fullXRange)
  }

  override fun getWidthMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
  ): List<Double> = getWeeklyLabelPositions(fullXRange)

  override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double? {
    val ranges = context.ranges
    return ranges.minX
  }

  override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double? {
    return null
  }

  override fun getLabelValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getWeeklyLabelPositions(fullXRange)
  }

  override fun getLineValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val weeklyLabels = getWeeklyLabelPositions(fullXRange)
    val allTicks = mutableListOf<Double>()

    // 1. 添加所有标签位置的主刻度线
    allTicks.addAll(weeklyLabels)

    // 2. 在整个范围内，每天添加一个刻度线
    val startDay = fullXRange.start.toLong()
    val endDay = fullXRange.endInclusive.toLong()

    for (day in startDay..endDay) {
      allTicks.add(day.toDouble())
    }

    return allTicks.sorted().distinct()
  }

  private fun getWeeklyLabelPositions(fullXRange: ClosedFloatingPointRange<Double>): List<Double> {
    val startEpochDay = fullXRange.start.toLong()
    val endEpochDay = fullXRange.endInclusive.toLong()

    val labelPositions = mutableListOf<Double>()

    // 从起始日期开始，每隔7天生成一个标签位置
    var currentDay = startEpochDay

    // 找到第一个对齐的7天边界（可选：对齐到周一等）
    // 这里简单地从起始日期开始，每7天一个标签
    while (currentDay <= endEpochDay) {
      labelPositions.add(currentDay.toDouble())
      currentDay += 7
    }

    return labelPositions
  }

  override fun getStartLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float = maxLabelWidth * 0.6f // 给第一个标签留足够的空间

  override fun getEndLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float = maxLabelWidth * 0.6f // 给最后一个标签留足够的空间
}

/**
 * 月度ItemPlacer - 专门用于月度图表显示
 *
 * 月度模式特性：
 * - 总是从月初开始显示标签
 * - 每隔一天展示一个刻度
 * - 每隔7天展示一个label
 * - 适用于按月查看的数据展示
 */
internal class OptimizedMonthlyItemPlacer : HorizontalAxis.ItemPlacer {

  override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

  override fun getHeightMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getOptimizedLabelPositions(fullXRange)
  }

  override fun getWidthMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
  ): List<Double> = getOptimizedLabelPositions(fullXRange)

  override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    val ranges = context.ranges
    return ranges.minX
  }

  override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    val ranges = context.ranges
    return ranges.maxX
  }

  override fun getLabelValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    return getOptimizedLabelPositions(fullXRange)
  }

  override fun getLineValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val labelPositions = getOptimizedLabelPositions(fullXRange)
    val allTicks = mutableListOf<Double>()

    allTicks.addAll(labelPositions)

    val startDay = fullXRange.start.toLong()
    val endDay = fullXRange.endInclusive.toLong()

    for (day in startDay..endDay) {
      allTicks.add(day.toDouble())
    }

    return allTicks.filter { it >= visibleXRange.start && it <= visibleXRange.endInclusive }
      .sorted()
      .distinct()
  }


  private fun getOptimizedLabelPositions(fullXRange: ClosedFloatingPointRange<Double>): List<Double> {
    val startEpochDay = fullXRange.start.toLong()
    val endEpochDay = fullXRange.endInclusive.toLong()
    val labelPositions = mutableListOf<Double>()

    val midPoint = (startEpochDay + endEpochDay) / 2
    val midDate = LocalDate.ofEpochDay(midPoint)
    val targetMonth = midDate.month
    val targetYear = midDate.year

    val firstDayOfTargetMonth = LocalDate.of(targetYear, targetMonth, 1)
    val monthFirstDay = firstDayOfTargetMonth.toEpochDay()

    var currentDay = monthFirstDay

    while (currentDay <= endEpochDay + 7) {
      if (currentDay >= startEpochDay - 14 && currentDay <= endEpochDay + 7) {
        labelPositions.add(currentDay.toDouble())
      }
      currentDay += 7
    }

    return labelPositions
  }

  override fun getStartLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float {
    return maxLabelWidth / 2f
  }

  override fun getEndLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float {
    return maxLabelWidth / 2f
  }
}
/**
 * 基于 OptimizedMonthlyItemPlacer 成功模式的30天ItemPlacer
 * 从minX开始，每7天一个标签，总共显示5个标签
 *
 * 关键改进：
 * - 完全模仿 OptimizedMonthlyItemPlacer 的简洁实现
 * - 从 minX 开始而不是从月初开始
 * - 允许标签适度超出数据范围，避免极端位置
 * - 使用简单的边距计算
 */
private class FixedThirtyDaysItemPlacer : HorizontalAxis.ItemPlacer {

  override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

  override fun getHeightMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> = getFixedLabelPositions(fullXRange)

  override fun getWidthMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
  ): List<Double> = getFixedLabelPositions(fullXRange)

  override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    return context.ranges.minX
  }

  override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    return context.ranges.maxX
  }

  override fun getLabelValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> = getFixedLabelPositions(fullXRange)

  override fun getLineValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val labelPositions = getFixedLabelPositions(fullXRange)
    val allTicks = mutableListOf<Double>()

    allTicks.addAll(labelPositions)

    val startDay = fullXRange.start.toLong()
    val endDay = fullXRange.endInclusive.toLong()

    for (day in startDay..endDay) {
      allTicks.add(day.toDouble())
    }

    return allTicks.filter { it >= visibleXRange.start && it <= visibleXRange.endInclusive }
      .sorted()
      .distinct()
  }

  override fun getStartLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float = 0f

  override fun getEndLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float = 0f

  /**
   * 核心标签位置计算：严格从 minX 开始，生成5个标签
   *
   * 30天模式的明确需求：
   * 1. 第一个标签必须是 minX
   * 2. 总共显示5个标签
   * 3. 每隔7天一个标签
   */
  private fun getFixedLabelPositions(fullXRange: ClosedFloatingPointRange<Double>): List<Double> {
    val startEpochDay = fullXRange.start.toLong()
    val endEpochDay = fullXRange.endInclusive.toLong()
    val labelPositions = mutableListOf<Double>()

    // 严格从 minX 开始，生成5个标签，每隔7天一个
    for (i in 0 until 5) {
      labelPositions.add((startEpochDay + i * 7).toDouble())
    }

    // 添加调试日志
    android.util.Log.d("FixedThirtyDaysItemPlacer", "🔍 fullXRange: ${fullXRange.start} - ${fullXRange.endInclusive}")
    android.util.Log.d("FixedThirtyDaysItemPlacer", "🔍 startEpochDay: $startEpochDay (${java.time.LocalDate.ofEpochDay(startEpochDay)})")
    android.util.Log.d("FixedThirtyDaysItemPlacer", "🔍 endEpochDay: $endEpochDay (${java.time.LocalDate.ofEpochDay(endEpochDay)})")
    android.util.Log.d("FixedThirtyDaysItemPlacer", "🔍 生成的标签位置: $labelPositions")
    android.util.Log.d("FixedThirtyDaysItemPlacer", "🔍 对应的日期: ${labelPositions.map { java.time.LocalDate.ofEpochDay(it.toLong()) }}")

    return labelPositions
  }
}

/**
 * 旧的30天模式ItemPlacer（已弃用）
 * 保留用于参考，实际使用 FixedThirtyDaysItemPlacer
 */
private class ThirtyDaysItemPlacer : HorizontalAxis.ItemPlacer {

  companion object {
    private const val TAG = "ThirtyDaysItemPlacer"
  }

  override fun getShiftExtremeLines(context: CartesianDrawingContext): Boolean = false

  override fun getHeightMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> = getThirtyDaysLabelPositions(fullXRange, context.ranges.minX, context.ranges.maxX)

  override fun getWidthMeasurementLabelValues(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    fullXRange: ClosedFloatingPointRange<Double>,
  ): List<Double> = getThirtyDaysLabelPositions(fullXRange, context.ranges.minX, context.ranges.maxX)

  override fun getFirstLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    val ranges = context.ranges
    // 30天模式：第一个标签必须是 ranges.minX
    val firstValue = ranges.minX
    Log.d(TAG, "🔍 getFirstLabelValue: ranges.minX=$firstValue (${LocalDate.ofEpochDay(firstValue.toLong())}), maxLabelWidth=$maxLabelWidth")
    return firstValue
  }

  override fun getLastLabelValue(context: CartesianMeasuringContext, maxLabelWidth: Float): Double {
    val ranges = context.ranges
    // 30天模式：动态计算实际生成的最后一个标签位置
    val fullXRange = ranges.minX..ranges.maxX
    val labelPositions = getThirtyDaysLabelPositions(fullXRange, ranges.minX, ranges.maxX)
    val lastValue = labelPositions.lastOrNull() ?: (ranges.minX + (4 * 7))
    Log.d(TAG, "🔍 getLastLabelValue: 实际最后标签位置=$lastValue (${LocalDate.ofEpochDay(lastValue.toLong())}), maxLabelWidth=$maxLabelWidth")
    Log.d(TAG, "🔍 所有标签位置: $labelPositions")
    return lastValue
  }

  override fun getLabelValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val labelPositions = getThirtyDaysLabelPositions(fullXRange, context.ranges.minX, context.ranges.maxX)
    Log.d(TAG, "🔍 getLabelValues: visibleXRange=$visibleXRange, fullXRange=$fullXRange")
    Log.d(TAG, "🔍 ranges: ${context.ranges.minX} - ${context.ranges.maxX}")
    Log.d(TAG, "🔍 ranges日期: ${LocalDate.ofEpochDay(context.ranges.minX.toLong())} - ${LocalDate.ofEpochDay(context.ranges.maxX.toLong())}")
    Log.d(TAG, "🔍 返回的标签位置: $labelPositions")
    Log.d(TAG, "🔍 对应的日期: ${labelPositions.map { LocalDate.ofEpochDay(it.toLong()) }}")
    return labelPositions
  }

  override fun getLineValues(
    context: CartesianDrawingContext,
    visibleXRange: ClosedFloatingPointRange<Double>,
    fullXRange: ClosedFloatingPointRange<Double>,
    maxLabelWidth: Float,
  ): List<Double> {
    val startEpochDay = fullXRange.start.toLong()
    val endEpochDay = fullXRange.endInclusive.toLong()
    val allTicks = mutableListOf<Double>()

    // 生成每日刻度线
    for (day in startEpochDay..endEpochDay) {
      allTicks.add(day.toDouble())
    }

    return allTicks.filter { it >= visibleXRange.start && it <= visibleXRange.endInclusive }.distinct()
  }

  override fun getStartLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float {
    // 30天模式：参考 OptimizedMonthlyItemPlacer 的成功实现
    // 使用 maxLabelWidth / 2f 来确保标签不被隐藏
    val margin = maxLabelWidth / 2f
    Log.d(TAG, "🔍 getStartLayerMargin: maxLabelWidth=$maxLabelWidth, margin=$margin")
    return margin
  }

  override fun getEndLayerMargin(
    context: CartesianMeasuringContext,
    layerDimensions: CartesianLayerDimensions,
    tickThickness: Float,
    maxLabelWidth: Float,
  ): Float {
    // 30天模式：为最后一个标签提供更多边距，确保完整显示
    // 增加边距来解决最后一个标签被隐藏的问题
    val margin = maxLabelWidth * 0.8f
    Log.d(TAG, "🔍 getEndLayerMargin: maxLabelWidth=$maxLabelWidth, margin=$margin")
    return margin
  }

  /**
   * 30天模式：从真实的minX开始，每7天一个标签，固定显示5个标签
   *
   * 关键策略（完全参考 OptimizedMonthlyItemPlacer 的成功模式）：
   * - 第一个标签必须是 ranges.minX
   * - 使用类似 OptimizedMonthlyItemPlacer 的范围扩展策略
   * - 允许标签超出 fullXRange，避免被放在极端刻度线上
   */
  private fun getThirtyDaysLabelPositions(
    fullXRange: ClosedFloatingPointRange<Double>,
    actualMinX: Double? = null,
    actualMaxX: Double? = null
  ): List<Double> {
    val labelPositions = mutableListOf<Double>()

    // 使用真实的 minX 作为起始点
    val realStartDay = actualMinX?.toLong() ?: fullXRange.start.toLong()
    val realEndDay = actualMaxX?.toLong() ?: fullXRange.endInclusive.toLong()

    // 从真实的 minX 开始，每隔7天生成标签
    var currentDay = realStartDay

    // 新策略：参考 OptimizedMonthlyItemPlacer，允许标签适度超出数据范围
    // 这样可以避免标签被放在极端位置而被隐藏
    val fifthLabelDay = realStartDay + (4 * 7)  // 第5个标签的位置

    Log.d(TAG, "🔍 fullXRange: ${fullXRange.start} - ${fullXRange.endInclusive}")
    Log.d(TAG, "🔍 realStartDay: $realStartDay (${LocalDate.ofEpochDay(realStartDay)})")
    Log.d(TAG, "🔍 realEndDay: $realEndDay (${LocalDate.ofEpochDay(realEndDay)})")
    Log.d(TAG, "🔍 第5个标签位置: $fifthLabelDay (${LocalDate.ofEpochDay(fifthLabelDay)})")
    Log.d(TAG, "🔍 距离结束天数: ${realEndDay - fifthLabelDay}")

    // 总是生成5个标签，但允许最后一个标签适度超出数据范围
    // 这样可以避免标签被放在极端的刻度线上
    for (i in 0 until 5) {
      labelPositions.add(currentDay.toDouble())
      currentDay += 7
    }

    Log.d(TAG, "🔍 策略：允许标签适度超出数据范围，避免极端位置")

    Log.d(TAG, "🔍 生成的标签位置: $labelPositions")
    Log.d(TAG, "🔍 对应的日期: ${labelPositions.map { LocalDate.ofEpochDay(it.toLong()) }}")

    return labelPositions
  }
}

internal fun calculateXAxisStep(
    chartGranularity: ChartGranularity,
): (CartesianChartModel) -> Double = {
    when (chartGranularity) {
        ChartGranularity.DAILY -> 10.0
        ChartGranularity.SIX_MONTHS -> 7.0
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> 1.0
    }
}
