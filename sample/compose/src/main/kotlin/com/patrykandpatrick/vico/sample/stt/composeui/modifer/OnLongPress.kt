package com.patrykandpatrick.vico.sample.stt.composeui.modifer

import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.PointerEventTimeoutCancellationException
import androidx.compose.ui.input.pointer.pointerInput

fun Modifier.onLongPress(
    key: Any?,
    onLongPressed: (Boolean) -> Unit,
): Modifier = pointerInput(key) {
    awaitPointerEventScope {
        while (true) {
            do {
                val event = awaitPointerEvent()
                var change = event.changes
                    .firstOrNull()
                    ?.takeIf { it.pressed }
                    ?: break

                try {
                    withTimeout(400L) {
                        while (true) {
                            change = awaitPointerEvent().changes.firstOrNull() ?: break
                            if (!change.pressed) {
                                break
                            }
                        }
                    }
                } catch (_: PointerEventTimeoutCancellationException) {
                    onLongPressed(true)
                }
            } while (change.pressed)

            onLongPressed(false)
        }
    }
}
