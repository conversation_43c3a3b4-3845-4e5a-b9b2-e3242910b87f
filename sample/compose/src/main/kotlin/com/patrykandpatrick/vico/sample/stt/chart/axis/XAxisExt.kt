package com.patrykandpatrick.vico.sample.stt.chart.axis

import android.content.Context
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.compose.cartesian.axis.fixed
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberAxisLabelComponent
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberAxisLineComponent
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberBottom
import com.patrykandpatrick.vico.compose.common.fill
import com.patrykandpatrick.vico.compose.common.insets
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.axis.BaseAxis
import com.patrykandpatrick.vico.core.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.composeui.theme.mediumGrey
import com.stt.android.chart.impl.chart.axis.createXAxisItemPlacer
import com.stt.android.chart.impl.chart.axis.createXAxisValueFormatter

@Composable
internal fun rememberXAxis(
  context: Context,
  chartData: ChartData,
): HorizontalAxis<Axis.Position.Horizontal.Bottom> = HorizontalAxis.rememberBottom(
    label = rememberAxisLabelComponent(
        textSize = with(LocalDensity.current) { 12.dp.toSp() },
        padding = insets(4.dp, 0.dp),
    ),
    line = rememberAxisLineComponent(
        fill = fill(MaterialTheme.colorScheme.mediumGrey),
    ),
    valueFormatter = remember(chartData.chartGranularity) {
        createXAxisValueFormatter(context, chartData.chartGranularity)
    },
    guideline = null,
    itemPlacer = remember(chartData.chartGranularity) {
        createXAxisItemPlacer(chartData.chartGranularity)
    },
    size = BaseAxis.Size.fixed(20.dp),
)
