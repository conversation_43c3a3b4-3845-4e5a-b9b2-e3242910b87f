package com.patrykandpatrick.vico.sample.stt.chart

import android.annotation.SuppressLint
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.text.TextPaint
import com.patrykandpatrick.vico.core.cartesian.CartesianDrawingContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.decoration.Decoration
import com.patrykandpatrick.vico.core.common.component.LineComponent
import com.patrykandpatrick.vico.core.common.data.ExtraStore

internal class AverageGuideLine(
    private val y: (ExtraStore) -> Double,
    private val line: LineComponent,
    private val label: (ExtraStore) -> String,
    private val labelColor: Int,
    private val labelBackgroundColor: Int,
    private val verticalAxisPosition: Axis.Position.Vertical,
) : Decoration {

    private val textPaint = TextPaint(TextPaint.ANTI_ALIAS_FLAG).apply {
        color = labelColor
    }

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = labelBackgroundColor
    }

    private val textBounds = Rect()

    private val indicatorPath = Path()

    @SuppressLint("RestrictedApi")
    override fun drawOverLayers(context: CartesianDrawingContext) = with(context) {
        val isStart = verticalAxisPosition == Axis.Position.Vertical.Start
        val yRange = ranges.getYRange(verticalAxisPosition)
        val y = y(model.extraStore)
        val canvasY =
            layerBounds.bottom - ((y - yRange.minY) / yRange.length).toFloat() * layerBounds.height()
        line.drawHorizontal(
            context,
            if (isStart) canvasBounds.left else layerBounds.left,
            if (isStart) layerBounds.right else canvasBounds.right,
            canvasY
        )

        val text = label(model.extraStore)
        val textWidth = textPaint.measureText(text, 0, text.length)
        val horizontalGap = 3f.pixels
        val boxHeight = 15f.pixels
        val triangleWidth = 5.62f.pixels
        buildIndicatorPath(
            if (isStart) canvasBounds.left else canvasBounds.right,
            canvasY - boxHeight / 2f,
            (textWidth + horizontalGap * 2).let { if (isStart) it else -it },
            triangleWidth.let { if (isStart) it else -it },
            boxHeight,
        )
        canvas.drawPath(indicatorPath, paint)
        textPaint.textSize = 12f.pixels
        textPaint.getTextBounds(text, 0, text.length, textBounds)
        canvas.drawText(
            text,
            if (isStart) canvasBounds.left + horizontalGap else canvasBounds.right - textWidth - horizontalGap,
            canvasY + textBounds.height() / 2f,
            textPaint,
        )
    }

    private fun buildIndicatorPath(
        x: Float,
        y: Float,
        rectangleWidth: Float,
        triangleWidth: Float,
        height: Float,
    ): Path {
        indicatorPath.reset()
        indicatorPath.moveTo(x, y)
        indicatorPath.lineTo(x + rectangleWidth, y)
        indicatorPath.lineTo(x + rectangleWidth + triangleWidth, y + height / 2f)
        indicatorPath.lineTo(x + rectangleWidth, y + height)
        indicatorPath.lineTo(x, y + height)
        indicatorPath.close()
        return indicatorPath
    }

    override fun equals(other: Any?): Boolean =
        this === other ||
            other is AverageGuideLine &&
            line == other.line &&
            label == other.label &&
            labelColor == other.labelColor &&
            labelBackgroundColor == other.labelBackgroundColor &&
            verticalAxisPosition == other.verticalAxisPosition

    override fun hashCode(): Int {
        var result = y.hashCode()
        result = 31 * result + line.hashCode()
        result = 31 * result + label.hashCode()
        result = 31 * result + labelColor.hashCode()
        result = 31 * result + labelBackgroundColor.hashCode()
        result = 31 * result + verticalAxisPosition.hashCode()
        return result
    }
}
