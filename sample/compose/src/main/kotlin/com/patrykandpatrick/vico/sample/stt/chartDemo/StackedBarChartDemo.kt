package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@Composable
fun StackedBarChartDemo(modifier: Modifier) {
    // 日期格式化工具
    val dateFormatter = remember { SimpleDateFormat("MM/dd", Locale.getDefault()) }

    // 格式化具体日期
    fun formatDate(dayOfYear: Int): String {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.YEAR, 2024)
        calendar.set(Calendar.DAY_OF_YEAR, dayOfYear)
        return dateFormatter.format(calendar.time)
    }

    // 创建堆叠柱状图数据
    val chartData = remember {
        // 设置X轴范围，对应6个月
        val startX = 1.0
        val endX = 6.0

        // 创建第一组柱状图数据（轻度活动）
        val lightActivity = ChartData.Series(
            chartType = ChartType.BAR,
            color = Color(0xFF34A853).toArgb(), // 绿色
            axisRange = ChartData.AxisRange(
                minX = startX,
                maxX = endX,
                minY = 0.0,
                maxY = 150.0
            ),
            entries = persistentListOf(
                ChartData.Entry(1, 45),
                ChartData.Entry(2, 52),
                ChartData.Entry(3, 38),
                ChartData.Entry(4, 42),
                ChartData.Entry(5, 55),
                ChartData.Entry(6, 47)
            ),
            value = buildAnnotatedString { append("轻度活动") },
            candlestickEntries = null,
            lineConfig = null,
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        )

        // 创建第二组柱状图数据（中度活动）
        val moderateActivity = ChartData.Series(
            chartType = ChartType.BAR,
            color = Color(0xFFFBBC05).toArgb(), // 黄色
            axisRange = ChartData.AxisRange(
                minX = startX,
                maxX = endX,
                minY = 0.0,
                maxY = 150.0
            ),
            entries = persistentListOf(
                ChartData.Entry(1, 65),
                ChartData.Entry(2, 72),
                ChartData.Entry(3, 58),
                ChartData.Entry(4, 67),
                ChartData.Entry(5, 78),
                ChartData.Entry(6, 63)
            ),
            value = buildAnnotatedString { append("中度活动") },
            candlestickEntries = null,
            lineConfig = null,
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        )

        // 创建第三组柱状图数据（高强度活动）
        val intenseActivity = ChartData.Series(
            chartType = ChartType.BAR,
            color = Color(0xFFEA4335).toArgb(), // 红色
            axisRange = ChartData.AxisRange(
                minX = startX,
                maxX = endX,
                minY = 0.0,
                maxY = 150.0
            ),
            entries = persistentListOf(
                ChartData.Entry(1, 25),
                ChartData.Entry(2, 32),
                ChartData.Entry(3, 18),
                ChartData.Entry(4, 22),
                ChartData.Entry(5, 35),
                ChartData.Entry(6, 28)
            ),
            value = buildAnnotatedString { append("高强度活动") },
            candlestickEntries = null,
            lineConfig = null,
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        )

        // 构建ChartData对象，设置为STACKED堆叠模式
        ChartData(
            chartContent = ChartContent.DURATION,
            chartGranularity = ChartGranularity.MONTHLY,
            series = persistentListOf(lightActivity, moderateActivity, intenseActivity),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            colorIndicator = null
        )
    }

    // 记录当前选中的项
    var selectedX by remember { mutableStateOf<Long?>(null) }

    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "不同强度活动时长堆叠图（分钟）",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        // 显示选中的数据
        selectedX?.let { xValue ->
            val month = when (xValue.toInt()) {
                1 -> "一月"
                2 -> "二月"
                3 -> "三月"
                4 -> "四月"
                5 -> "五月"
                6 -> "六月"
                else -> "未知"
            }

            val lightValue = chartData.series[0].entries.find { it.x == xValue }?.y?.toString() ?: "-"
            val moderateValue = chartData.series[1].entries.find { it.x == xValue }?.y?.toString() ?: "-"
            val intenseValue = chartData.series[2].entries.find { it.x == xValue }?.y?.toString() ?: "-"

            // 计算总时长
            val totalValue = (lightValue.toFloatOrNull() ?: 0f) +
                            (moderateValue.toFloatOrNull() ?: 0f) +
                            (intenseValue.toFloatOrNull() ?: 0f)

            Text(
                text = "$month: 轻度 $lightValue 分钟, 中度 $moderateValue 分钟, 高强度 $intenseValue 分钟",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            Text(
                text = "总活动时长: $totalValue 分钟",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }

        // 渲染图表
        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedX = x },
            onNoEntrySelected = { selectedX = null },
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
                .padding(16.dp)
        )
    }
}
