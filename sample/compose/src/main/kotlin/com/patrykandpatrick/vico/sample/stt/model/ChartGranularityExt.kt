package com.patrykandpatrick.vico.sample.stt.model

import com.patrykandpatrick.vico.sample.compose.R
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity

internal val ChartGranularity.highlightTitleRes: Int
    get() = when (this) {
        ChartGranularity.DAILY,
        ChartGranularity.WEEKLY,
        ChartGranularity.SEVEN_DAYS,
        ChartGranularity.MONTHLY,
        ChartGranularity.THIRTY_DAYS,
        ChartGranularity.SIXTY_DAYS,
        ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
        ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
        ChartGranularity.SIX_WEEKS -> R.string.chart_value_total
        ChartGranularity.SIX_MONTHS,
        ChartGranularity.YEARLY,
        ChartGranularity.EIGHT_YEARS -> R.string.chart_value_daily_avg
    }

internal val ChartGranularity.abbreviatedNameRes: Int get() = when (this) {
    ChartGranularity.DAILY -> R.string.chart_granularity_daily_abbreviation
    ChartGranularity.WEEKLY -> R.string.chart_granularity_weekly_abbreviation
    ChartGranularity.SEVEN_DAYS -> R.string.chart_granularity_seven_days_abbreviation
    ChartGranularity.MONTHLY -> R.string.chart_granularity_monthly_abbreviation
    ChartGranularity.THIRTY_DAYS -> R.string.chart_granularity_thirty_days_abbreviation
    ChartGranularity.SIXTY_DAYS -> R.string.chart_granularity_sixty_day_days_abbreviation
    ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> R.string.chart_granularity_one_hundred_eight_days_abbreviation
    ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> R.string.chart_granularity_three_hundred_sixty_five_days_abbreviation
    ChartGranularity.SIX_WEEKS -> R.string.chart_granularity_six_weeks_abbreviation
    ChartGranularity.SIX_MONTHS -> R.string.chart_granularity_six_months_abbreviation
    ChartGranularity.YEARLY -> R.string.chart_granularity_yearly_abbreviation
    ChartGranularity.EIGHT_YEARS -> R.string.chart_granularity_eight_years_abbreviation
}

internal val ChartGranularity.nameRes: Int get() = when (this) {
    ChartGranularity.DAILY -> TODO()
    ChartGranularity.WEEKLY -> TODO()
    ChartGranularity.SEVEN_DAYS -> R.string.chart_granularity_seven_days
    ChartGranularity.MONTHLY -> TODO()
    ChartGranularity.THIRTY_DAYS -> R.string.chart_granularity_thirty_days
    ChartGranularity.SIXTY_DAYS,
    ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
    ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> TODO()
    ChartGranularity.SIX_WEEKS -> R.string.chart_granularity_six_weeks
    ChartGranularity.SIX_MONTHS -> R.string.chart_granularity_six_months
    ChartGranularity.YEARLY -> TODO()
    ChartGranularity.EIGHT_YEARS -> R.string.chart_granularity_eight_years
}

internal val ChartGranularity.intervalNameRes: Int get() = when (this) {
    ChartGranularity.DAILY -> TODO()
    ChartGranularity.WEEKLY,
    ChartGranularity.SEVEN_DAYS,
    ChartGranularity.MONTHLY,
    ChartGranularity.THIRTY_DAYS,
    ChartGranularity.SIXTY_DAYS,
    ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
    ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
    ChartGranularity.SIX_WEEKS -> R.string.chart_granularity_daily_interval
    ChartGranularity.SIX_MONTHS -> R.string.chart_granularity_weekly_interval
    ChartGranularity.YEARLY -> R.string.chart_granularity_monthly_interval
    ChartGranularity.EIGHT_YEARS -> R.string.chart_granularity_yearly_interval
}

val ChartGranularity.candleSpacing:Float get() = when (this) {
  ChartGranularity.WEEKLY,
  ChartGranularity.SEVEN_DAYS -> 24f
  ChartGranularity.DAILY,
  ChartGranularity.MONTHLY,
  ChartGranularity.THIRTY_DAYS,
  ChartGranularity.SIXTY_DAYS,
  ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
  ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
  ChartGranularity.SIX_WEEKS,
  ChartGranularity.SIX_MONTHS,
  ChartGranularity.YEARLY,
  ChartGranularity.EIGHT_YEARS -> 4f
}
