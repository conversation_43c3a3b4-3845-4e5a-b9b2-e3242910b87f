package com.patrykandpatrick.vico.sample.stt.utils

import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime

fun ZonedDateTime.copy(): ZonedDateTime = this.plusSeconds(0)

/**
 * @return [LocalDateTime] at midnight between this [LocalDate] and next [LocalDate]
 */
fun LocalDate.atEndOfDay(): LocalDateTime = plusDays(1).atStartOfDay()

fun LocalDateTime.toEpochMilli(atZone: ZoneId = ZoneId.systemDefault()): Long =
    atZone(atZone).toInstant().toEpochMilli()

/**
 * Iterator to allow iterating a through [LocalDate] range like
 * <code>for (date in firstDay..lastDay) { ... }</code>
 */
operator fun ClosedRange<LocalDate>.iterator() = object : Iterator<LocalDate> {
    var value = start
    override fun hasNext(): Boolean {
        return value <= endInclusive
    }

    override fun next(): LocalDate {
        val ret = value
        value = value.plusDays(1)
        return ret
    }
}

fun Duration.toDaysPartSafe(): Long = seconds / (24 * 60 * 60)
fun Duration.toHoursPartSafe(): Int = (toHours() % 24).toInt()
fun Duration.toMinutesPartSafe(): Int = (toMinutes() % 60).toInt()
fun Duration.toSecondsPartSafe(): Int = (toSeconds() % 60).toInt()
