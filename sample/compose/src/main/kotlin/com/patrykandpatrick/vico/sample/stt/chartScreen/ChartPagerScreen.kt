package com.patrykandpatrick.vico.sample.stt.chartScreen

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import android.util.Log

/**
 * 图表分页展示屏幕
 * 使用 HorizontalPager 实现多个图表的滑动切换
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ChartPagerScreen(modifier: Modifier = Modifier) {
    // 定义图表数据集合 - 生成5组随机折线图数据
    val chartDataList = remember {
        listOf(
            createRandomLineChartData(1),
            createRandomLineChartData(2),
            createRandomLineChartData(3),
            createRandomLineChartData(4),
            createRandomLineChartData(5)
        )
    }

    // 图表信息
    val chartInfoList = listOf(
        ChartInfo("折线图 1", "随机数据趋势展示"),
        ChartInfo("折线图 2", "随机数据趋势展示"),
        ChartInfo("折线图 3", "随机数据趋势展示"),
        ChartInfo("折线图 4", "随机数据趋势展示"),
        ChartInfo("折线图 5", "随机数据趋势展示")
    )

    // 创建 Pager 状态
    val pagerState = rememberPagerState(pageCount = { chartDataList.size })

    // 当前选中的数据点
    var selectedX by remember { mutableStateOf<Long?>(null) }

    // 协程作用域用于ViewPager滑动
    val scope = rememberCoroutineScope()

    // 移除用户滑动状态跟踪，完全由Chart组件控制手势

    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 顶部标题区域
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "图表展示中心",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = MaterialTheme.colorScheme.primary
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 当前页面信息
                Text(
                    text = "${pagerState.currentPage + 1} / ${chartDataList.size}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )

                Spacer(modifier = Modifier.height(4.dp))

                // 当前图表标题和描述
                Text(
                    text = chartInfoList[pagerState.currentPage].title,
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.SemiBold
                    ),
                    textAlign = TextAlign.Center
                )

                Text(
                    text = chartInfoList[pagerState.currentPage].description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
            }
        }

                // 水平分页器 - 完全禁用用户滑动，只通过程序控制
        HorizontalPager(
            state = pagerState,
            userScrollEnabled = false, // 完全禁用用户滑动
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) { page ->
            // 每个页面包含一个 Chart 组件
            Card(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(8.dp)
                ) {
                    // 使用 Chart 组件渲染对应的图表数据
                    Chart(
                        chartData = chartDataList[page],
                        onEntrySelected = { x -> selectedX = x },
                        onNoEntrySelected = { selectedX = null },
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }

        // 页面指示器
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = androidx.compose.foundation.layout.Arrangement.Center
        ) {
            repeat(chartDataList.size) { index ->
                val isSelected = index == pagerState.currentPage
                Box(
                    modifier = Modifier
                        .size(if (isSelected) 12.dp else 8.dp)
                        .clip(CircleShape)
                        .background(
                            if (isSelected) {
                                MaterialTheme.colorScheme.primary
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f)
                            }
                        )
                )

                if (index < chartDataList.size - 1) {
                    Spacer(modifier = Modifier.width(8.dp))
                }
            }
        }
    }
}

/**
 * 图表信息数据类
 */
private data class ChartInfo(
    val title: String,
    val description: String
)

/**
 * 创建随机折线图数据
 */
private fun createRandomLineChartData(seed: Int): ChartData {
    val startX = 1.0
    val endX = 30.0

    // 使用种子生成可重复的随机数据
    val random = kotlin.random.Random(seed)

    // 定义不同的颜色
    val colors = listOf(
        Color(0xFF34A853).toArgb(), // 绿色
        Color(0xFF4285F4).toArgb(), // 蓝色
        Color(0xFFEA4335).toArgb(), // 红色
        Color(0xFFFF5722).toArgb(), // 橙色
        Color(0xFF9C27B0).toArgb()  // 紫色
    )

    // 生成随机数据点，确保趋势合理
    val entries = mutableListOf<ChartData.Entry>()
    var baseValue = random.nextDouble(20.0, 40.0) // 基础值

    for (x in 1..30 step 2) {
        // 添加一些随机波动，但保持整体趋势
        val variation = random.nextDouble(-15.0, 15.0)
        val newValue = (baseValue + variation).coerceIn(10.0, 90.0)
        entries.add(ChartData.Entry(x.toLong(), newValue))

        // 基础值也稍微变化，形成趋势
        baseValue += random.nextDouble(-5.0, 5.0)
        baseValue = baseValue.coerceIn(15.0, 75.0)
    }

    val lineData = ChartData.Series(
        chartType = ChartType.LINE,
        color = colors[(seed - 1) % colors.size],
        axisRange = ChartData.AxisRange(startX, endX, 0.0, 100.0),
        entries = persistentListOf(*entries.toTypedArray()),
        value = buildAnnotatedString { append("数据系列 $seed") },
        candlestickEntries = null,
        lineConfig = LineChartConfig(
            isSmoothCurve = random.nextBoolean(), // 随机决定是否平滑
            showPoints = random.nextBoolean(),    // 随机显示数据点
            pointSizeDP = random.nextFloat() * 4 + 4f, // 4-8dp
            isPointFilled = random.nextBoolean(),
            showAreaFill = random.nextBoolean(),  // 随机显示面积填充
            areaAlpha = random.nextFloat() * 0.3f + 0.2f, // 0.2-0.5
            thicknessDP = random.nextFloat() * 2f + 2f // 2-4dp
        ),
        gradientEntries = persistentListOf(),
        backgroundRegion = null,
        groupStackBarStyle = null,
        groupStackBarEntries = persistentListOf()
    )

    return ChartData(
        chartContent = ChartContent.STEPS,
        chartGranularity = ChartGranularity.MONTHLY,
        series = persistentListOf(lineData),
        highlightEnabled = true,
        goal = if (random.nextBoolean()) random.nextInt(50, 80) else null, // 随机添加目标线
        highlightDecorationLines = persistentMapOf(),
        chartBarDisplayMode = ChartBarDisplayMode.STACKED,
        colorIndicator = null
    )
}

