package com.patrykandpatrick.vico.sample.stt.model

data class LineChartConfig(
    val isSmoothCurve: Boolean = true,
    val showPoints: Boolean = false,
    val pointSizeDP: Float? = null,
    val isPointFilled: Boolean = true,
    val showAreaFill: Boolean = true,
    val areaAlpha: Float? = null,
    val thicknessDP: Float? = null,
) {
    companion object {

        fun smoothWithPoints(
            pointSize: Float? = null,
            isPointFilled: Boolean = true
        ): LineChartConfig = LineChartConfig(
            isSmoothCurve = true,
            showPoints = true,
            pointSizeDP = pointSize,
            isPointFilled = isPointFilled
        )
        fun straight(
            showPoints: Boolean = false,
            pointSize: Float? = null,
            isPointFilled: Boolean = true,
            showAreaFill: Boolean = false,
            thickness: Float? = null
        ): LineChartConfig = LineChartConfig(
            isSmoothCurve = false,
            showPoints = showPoints,
            pointSizeDP = pointSize,
            isPointFilled = isPointFilled,
            showAreaFill = showAreaFill,
            thicknessDP = thickness
        )
        fun noFill(
            isSmoothCurve: Boolean = true,
            showPoints: Boolean = false,
            pointSize: Float? = null,
            isPointFilled: Boolean = true,
            thickness: Float? = null
        ): LineChartConfig = LineChartConfig(
            isSmoothCurve = isSmoothCurve,
            showPoints = showPoints,
            pointSizeDP = pointSize,
            isPointFilled = isPointFilled,
            showAreaFill = false,
            thicknessDP = thickness
        )
    }
}
