package com.patrykandpatrick.vico.sample.stt.utils

import android.content.Context
import android.text.format.DateUtils
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.text.FieldPosition
import java.util.Formatter
import java.util.Locale

object TextFormatter {
    private var decimalFormatSymbols: DecimalFormatSymbols? = null

    private fun getDecimalFormatSymbols(): DecimalFormatSymbols {
        if (decimalFormatSymbols == null) {
            // We want to always use English formatting for numeric data regardless of the
            // selected locale.
            decimalFormatSymbols = DecimalFormatSymbols(Locale.ENGLISH)
        }
        return decimalFormatSymbols!!
    }

    private val STRING_BUFFER = StringBuffer()
    private val ZERO_FIELD_POSITION = FieldPosition(0)

    private var distanceFormatter: DecimalFormat? = null

    private val VALUE_CHARS = "0123456789:'" +
            getDecimalFormatSymbols().decimalSeparator +
            getDecimalFormatSymbols().minusSign +
            getDecimalFormatSymbols().infinity

    private val distanceDecimalFormat: DecimalFormat
        get() {
            if (distanceFormatter == null) {
                distanceFormatter =
                    DecimalFormat(
                        "0.00",
                        getDecimalFormatSymbols()
                    )
            }
            return distanceFormatter!!
        }

    fun formatDistance(distance: Double): String {
        synchronized(STRING_BUFFER) {
            STRING_BUFFER.setLength(0)
            return distanceDecimalFormat.format(distance, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString()
        }
    }

    private var distanceFormatterWithoutZero: DecimalFormat? = null

    private val distanceWithoutZeroDecimalFormat: DecimalFormat
        get() {
            if (distanceFormatterWithoutZero == null) {
                distanceFormatterWithoutZero =
                    DecimalFormat(
                        "0.#",
                        getDecimalFormatSymbols()
                    )
            }
            return distanceFormatterWithoutZero!!
        }

    fun formatDistanceWithoutZeros(distance: Double): String {
        synchronized(STRING_BUFFER) {
            STRING_BUFFER.setLength(0)
            return distanceWithoutZeroDecimalFormat.format(
                distance, STRING_BUFFER,
                ZERO_FIELD_POSITION
            ).toString()
        }
    }

    private var distanceFormatterRounded: DecimalFormat? = null

    private val distanceRoundedFormat: DecimalFormat
        get() {
            if (distanceFormatterRounded == null) {
                distanceFormatterRounded =
                    DecimalFormat(
                        "0",
                        getDecimalFormatSymbols()
                    )
            }
            return distanceFormatterRounded!!
        }

    fun formatDistanceRounded(distance: Double): String {
        synchronized(STRING_BUFFER) {
            STRING_BUFFER.setLength(0)
            return distanceRoundedFormat.format(
                distance, STRING_BUFFER,
                ZERO_FIELD_POSITION
            ).toString()
        }
    }

    private var speedFormatter: DecimalFormat? = null

    private val speedDecimalFormat: DecimalFormat
        get() {
            if (speedFormatter == null) {
                speedFormatter =
                    DecimalFormat(
                        "0.0",
                        getDecimalFormatSymbols()
                    )
            }
            return speedFormatter!!
        }

    fun formatSpeed(speed: Double): String {
        synchronized(STRING_BUFFER) {
            STRING_BUFFER.setLength(0)
            return speedDecimalFormat.format(speed, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString()
        }
    }

    private var angleFormatter: DecimalFormat? = null

    private val angleDecimalFormat: DecimalFormat
        get() {
            if (angleFormatter == null) {
                angleFormatter =
                    DecimalFormat(
                        "0.0",
                        getDecimalFormatSymbols()
                    )
            }
            return angleFormatter!!
        }

    fun formatAngle(angle: Double): String {
        synchronized(STRING_BUFFER) {
            STRING_BUFFER.setLength(0)
            return angleDecimalFormat.format(angle, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString()
        }
    }

    private var altitudeFormatter: DecimalFormat? = null

    private val altitudeDecimalFormat: DecimalFormat
        get() {
            if (altitudeFormatter == null) {
                altitudeFormatter =
                    DecimalFormat(
                        "0",
                        getDecimalFormatSymbols()
                    )
            }
            return altitudeFormatter!!
        }

    fun formatAltitude(altitude: Double): String {
        synchronized(STRING_BUFFER) {
            STRING_BUFFER.setLength(0)
            return altitudeDecimalFormat.format(altitude, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString()
        }
    }



    private var gasConsumptionFormatter: DecimalFormat? = null

    private val gasConsumptionDecimalFormat: DecimalFormat
        get() {
            if (gasConsumptionFormatter == null) {
                gasConsumptionFormatter =
                    DecimalFormat(
                        "0.#",
                        getDecimalFormatSymbols()
                    )
            }
            return gasConsumptionFormatter!!
        }

    fun formatGasConsumption(gasConsumption: Double): String {
        synchronized(STRING_BUFFER) {
            STRING_BUFFER.setLength(0)
            return gasConsumptionDecimalFormat.format(
                gasConsumption, STRING_BUFFER,
                ZERO_FIELD_POSITION
            )
                .toString()
        }
    }

    private var personalSettingFormatter: DecimalFormat? = null

    private val personalSettingDecimalFormat: DecimalFormat
        get() {
            if (personalSettingFormatter == null) {
                personalSettingFormatter =
                    DecimalFormat(
                        "0",
                        decimalFormatSymbols
                    )
            }
            return personalSettingFormatter!!
        }

    fun formatPersonalSetting(personalSetting: Int): String {
        synchronized(STRING_BUFFER) {
            val format =
                personalSettingDecimalFormat
            val prefix = if (personalSetting > 0) "+" else ""
            format.positivePrefix = prefix
            STRING_BUFFER.setLength(0)
            return format.format(
                personalSetting.toLong(), STRING_BUFFER,
                ZERO_FIELD_POSITION
            )
                .toString()
        }
    }


    private var pressureFormatter: DecimalFormat? = null

    private val pressureDecimalFormat: DecimalFormat
        get() {
            if (pressureFormatter == null) {
                pressureFormatter =
                    DecimalFormat(
                        "0",
                        decimalFormatSymbols
                    )
            }
            return pressureFormatter!!
        }

    private var percentageFormatter: DecimalFormat? = null

    private val percentageDecimalFormat: DecimalFormat
        get() {
            if (percentageFormatter == null) {
                percentageFormatter =
                    DecimalFormat(
                        "0",
                        decimalFormatSymbols
                    )
            }
            return percentageFormatter!!
        }

    fun formatPercentage(percentage: Double): String {
        synchronized(STRING_BUFFER) {
            STRING_BUFFER.setLength(0)
            return percentageDecimalFormat.format(percentage, STRING_BUFFER, ZERO_FIELD_POSITION)
                .toString()
        }
    }

    fun formatDateTime(context: Context, timestamp: Long): String {
        return formatDateTime(
            context, timestamp, (DateUtils.FORMAT_SHOW_TIME
                    or DateUtils.FORMAT_SHOW_DATE
                    or DateUtils.FORMAT_NUMERIC_DATE)
        )
    }

    fun formatTime(context: Context, timestamp: Long): String {
        return formatDateTime(context, timestamp, DateUtils.FORMAT_SHOW_TIME)
    }

    // 替换为一个简单的字符串缓存，而不是使用StringBuilder
    private val TIME_FORMAT_CACHE = mutableListOf<String>()
    private val FORMATTER = Formatter()

    private fun formatDateTime(context: Context, timestamp: Long, flag: Int): String {
        synchronized(TIME_FORMAT_CACHE) {
            // 使用DateUtils直接返回格式化的字符串
            return DateUtils.formatDateRange(context, timestamp, timestamp, flag)
        }
    }

    fun formatDate(context: Context, timestamp: Long): String {
        return formatDateTime(
            context, timestamp,
            DateUtils.FORMAT_SHOW_DATE or DateUtils.FORMAT_NUMERIC_DATE
        )
    }

    fun formatDate(context: Context, timestamp: Long, showYear: Boolean): String {
        return formatDateTime(
            context, timestamp,
            if (showYear)
                DateUtils.FORMAT_SHOW_DATE or DateUtils.FORMAT_NUMERIC_DATE or DateUtils.FORMAT_SHOW_YEAR
            else
                DateUtils.FORMAT_SHOW_DATE or DateUtils.FORMAT_NUMERIC_DATE or DateUtils.FORMAT_NO_YEAR
        )
    }

    private var jumpHeightFormatter: DecimalFormat? = null
        get() {
            if (field == null) {
                field = DecimalFormat(
                    "0.0",
                    decimalFormatSymbols
                )
            }
            return field
        }

    private var maxOneDecimalFormatter: DecimalFormat? = null
        get() {
            if (field == null) {
                field = DecimalFormat(
                    "#.#",
                    decimalFormatSymbols
                )
            }
            return field
        }

    fun formatVo2Max(vo2Max: Float): String {
        synchronized(STRING_BUFFER) {
            STRING_BUFFER.setLength(0)
            return maxOneDecimalFormatter!!.format(
                vo2Max.toDouble(),
                STRING_BUFFER,
                ZERO_FIELD_POSITION
            ).toString()
        }
    }

    @Deprecated("")
    fun formatElapsedTime(seconds: Long): String {
        return formatElapsedTime(seconds, true, true)
    }

    @Deprecated("")
    fun formatElapsedTime(seconds: Long, showHour: Boolean): String {
        return formatElapsedTime(seconds, showHour, true)
    }

    @Deprecated("")
    private fun formatElapsedTime(seconds: Long, showHour: Boolean, showSecond: Boolean): String {
        var h = 0L
        var m = 0L
        val s: Long
        if (seconds >= 60L) {
            if (seconds >= 3600L) {
                h = seconds / 3600L
                m = (seconds % 3600L) / 60L
            } else {
                m = seconds / 60L
            }
            s = seconds % 60L
        } else {
            s = seconds
        }

        // 替换StringBuilder的使用，直接使用字符串拼接
        return buildString {
            if (showHour) {
                if (h < 10L) {
                    append('0')
                }
                append(h).append(':')
            }

            if (m < 10L) {
                append('0')
            }
            append(m)

            if (showSecond) {
                append(':')
                if (s < 10L) {
                    append('0')
                }
                append(s)
            }
        }
    }
}
