package com.patrykandpatrick.vico.sample.stt.chart

import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.core.cartesian.data.candlestickSeries
import com.patrykandpatrick.vico.core.cartesian.data.columnSeries
import com.patrykandpatrick.vico.core.cartesian.data.lineSeries
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType

internal suspend fun CartesianChartModelProducer.prepareData(
  chartData: ChartData,
  rangeProvider: RangeProvider,
) {
    // 找到所有MultiColorColumnComponent实例，并重置它们的绘制状态
    chartData.series
        .filter { it.chartType == ChartType.BAR && it.groupStackBarEntries.isNotEmpty() }
        .forEach { _ ->
            // 重置所有多色柱状图组件的绘制状态
            MultiColorColumnComponent.resetAllInstances()
        }

    runTransaction {
        rangeProvider.chartData = chartData

        val isGroupedBarChart = chartData.chartBarDisplayMode == ChartBarDisplayMode.GROUPED &&
            chartData.series.count { it.chartType == ChartType.BAR } > 1

        if (isGroupedBarChart) {
            processGroupedData(chartData)
        } else {
            val isStackedBarChart = chartData.chartBarDisplayMode == ChartBarDisplayMode.STACKED &&
                chartData.series.count { it.chartType == ChartType.BAR } > 1 &&
                chartData.series.all { it.chartType == ChartType.BAR }
            if (isStackedBarChart) {
                processStackedBarData(chartData)
            } else {
                processDefaultData(chartData)
            }
        }
    }
}

private fun CartesianChartModelProducer.Transaction.processGroupedData(chartData: ChartData) {
    chartData.series
        .filter { it.chartType == ChartType.BAR }
        .let { barSeries ->
            columnSeries {
                barSeries.forEach { series ->
                    if (series.groupStackBarEntries.isNotEmpty()) {
                        // 处理堆叠分组数据
                        // 获取唯一的 x 值集合
                        val uniqueXValues = series.groupStackBarEntries.map { it.x }.distinct()

                        // 为每个 x 值计算一个总和的 y 值
                        val ySum = uniqueXValues.map { x ->
                            val entriesForX = series.groupStackBarEntries.filter { it.x == x }
                            entriesForX.sumOf { it.y.toDouble() }
                        }

                        // 发送为一个数据系列
                        series(uniqueXValues, ySum)
                    } else {
                        val xyData = extractXYData(series.entries)
                        series(xyData.first, xyData.second)
                    }
                }
            }
        }

    chartData.series
        .forEach { series ->
            when (series.chartType) {
                ChartType.BAR -> Unit
                ChartType.LINE -> processLineSeries(series)
                ChartType.CANDLESTICK -> processCandlestickSeries(series)
                ChartType.SLEEP_STAGE -> Unit
            }
        }
}

private fun CartesianChartModelProducer.Transaction.processDefaultData(chartData: ChartData) {
    chartData.series.forEach { series ->
        when (series.chartType) {
            ChartType.BAR -> processBarSeries(series)
            ChartType.LINE -> processLineSeries(series)
            ChartType.CANDLESTICK -> processCandlestickSeries(series)
            ChartType.SLEEP_STAGE -> Unit
        }
    }
}

private fun CartesianChartModelProducer.Transaction.processStackedBarData(chartData: ChartData) {
    columnSeries {
        chartData.series.forEach { series ->
            if (series.groupStackBarEntries.isNotEmpty()) {
                // 处理堆叠分组数据
                // 获取唯一的 x 值集合
                val uniqueXValues = series.groupStackBarEntries.map { it.x }.distinct()

                // 为每个 x 值计算一个总和的 y 值
                val ySum = uniqueXValues.map { x ->
                    val entriesForX = series.groupStackBarEntries.filter { it.x == x }
                    entriesForX.sumOf { it.y.toDouble() }
                }

                // 发送为一个数据系列
                series(uniqueXValues, ySum)
            } else {
                val xyData = extractXYData(series.entries)
                series(xyData.first, xyData.second)
            }
        }
    }
}

private fun CartesianChartModelProducer.Transaction.processBarSeries(series: ChartData.Series) {
    if (series.groupStackBarEntries.isNotEmpty()) {
        // 处理堆叠分组数据
        val uniqueXValues = series.groupStackBarEntries.map { it.x }.distinct()
        val ySum = uniqueXValues.map { x ->
            val entriesForX = series.groupStackBarEntries.filter { it.x == x }
            entriesForX.sumOf { it.y.toDouble() }
        }

        columnSeries {
            series(uniqueXValues, ySum)
        }
    } else {
        val xyData = extractXYData(series.entries)
        columnSeries {
            series(xyData.first, xyData.second)
        }
    }
}

private fun CartesianChartModelProducer.Transaction.processLineSeries(series: ChartData.Series) {
    val xyData = if (series.entries.isNotEmpty()) {
        extractXYData(series.entries)
    } else if (series.gradientEntries.isNotEmpty()) {
        extractXYDataFromGradient(series.gradientEntries)
    } else {
        listOf(0.0) to listOf(0.0)
    }

    lineSeries {
        series(xyData.first, xyData.second)
    }
}

private fun extractXYData(entries: List<ChartData.Entry>): Pair<List<Number>, List<Number>> {
    val x = entries.map { it.x }.takeIf { it.isNotEmpty() } ?: listOf(0.0)
    val y = entries.map { it.y }.takeIf { it.isNotEmpty() } ?: listOf(0.0)
    return x to y
}

private fun extractXYDataFromGradient(entries: List<ChartData.GradientLineEntry>): Pair<List<Number>, List<Number>> {
    val x = entries.map { it.x }.takeIf { it.isNotEmpty() } ?: listOf(0.0)
    val y = entries.map { it.y }.takeIf { it.isNotEmpty() } ?: listOf(0.0)
    return x to y
}


private fun CartesianChartModelProducer.Transaction.processCandlestickSeries(series: ChartData.Series) {
    series.candlestickEntries?.takeIf { it.isNotEmpty() }?.let { entries ->
        val xValues = mutableListOf<Number>()
        val openValues = mutableListOf<Number>()
        val closeValues = mutableListOf<Number>()
        val lowValues = mutableListOf<Number>()
        val highValues = mutableListOf<Number>()

        entries.forEach { entry ->
            xValues.add(entry.x)
            openValues.add(entry.open)
            closeValues.add(entry.close)
            lowValues.add(entry.low)
            highValues.add(entry.high)
        }

        candlestickSeries(xValues, openValues, closeValues, lowValues, highValues)
    } ?: run {
        val minX = series.axisRange.minX
        val maxX = series.axisRange.maxX
        val minY = series.axisRange.minY - 1.0f  // Subtract 1.0f to ensure candlestick chart is completely hidden when using default empty data

        val xValues = listOf(minX, maxX)
        val openValues = listOf(minY, minY)
        val closeValues = listOf(minY, minY)
        val lowValues = listOf(minY, minY)
        val highValues = listOf(minY, minY)

        candlestickSeries(xValues, openValues, closeValues, lowValues, highValues)
    }
}
