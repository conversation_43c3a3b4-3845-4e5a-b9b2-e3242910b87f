package com.patrykandpatrick.vico.sample.stt.model

internal data class DailySleepMetricsData(
  val sleepQuality: DailySleepQualityData?,
  val sleepDuration: DailySleepDurationData?,
  val resources: DailySleepResourcesData?,
)

internal data class DailySleepQualityData(
    val quality: Float,
    val qualityDesc: String?,
    val avgHrInBpm: Int?,
    val minHrInBpm: Int?,
    val avgHrv: Int?,
    val maxSpO2: Float?,
    val altitude: Float?,
)

internal data class DailySleepDurationData(
  val longSleep: SleepPeriod?,
  val naps: List<SleepPeriod>,
  val longSleepSeconds: Long,
  val napSeconds: Long,
  val totalSeconds: Long,
  val goalSeconds: Long,
)

internal data class SleepPeriod(
    val fellAsleep: String,
    val wokeUp: String,
    val totalSeconds: Long,
)

internal data class DailySleepResourcesData(
    val wakeUpBalance: Float,
    val gainedBalance: Float?,
)
