/*
 * Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.patrykandpatrick.vico.sample.stt

import androidx.compose.foundation.layout.height
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.compose.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberBottom
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberStart
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberCandlestickCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberLineCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.layer.point
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberLine
import com.patrykandpatrick.vico.compose.cartesian.rememberCartesianChart
import com.patrykandpatrick.vico.compose.common.component.rememberLineComponent
import com.patrykandpatrick.vico.compose.common.component.rememberShapeComponent
import com.patrykandpatrick.vico.compose.common.component.rememberTextComponent
import com.patrykandpatrick.vico.compose.common.fill
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.core.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.core.cartesian.data.CartesianLayerRangeProvider
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.patrykandpatrick.vico.core.cartesian.data.CandlestickCartesianLayerModel
import com.patrykandpatrick.vico.core.cartesian.data.candlestickSeries
import com.patrykandpatrick.vico.core.cartesian.data.lineSeries
import com.patrykandpatrick.vico.core.cartesian.layer.CandlestickCartesianLayer
import com.patrykandpatrick.vico.core.cartesian.layer.LineCartesianLayer
import com.patrykandpatrick.vico.core.cartesian.marker.DefaultCartesianMarker
import com.patrykandpatrick.vico.core.common.data.ExtraStore
import com.patrykandpatrick.vico.core.common.shape.CorneredShape
import com.patrykandpatrick.vico.sample.compose.PreviewBox
import java.text.DecimalFormat
import kotlin.math.ceil
import kotlin.math.floor
import kotlinx.coroutines.runBlocking

// 定义图表常量
private const val Y_STEP = 40.0

// 图表颜色
private val CANDLE_COLOR = Color(0xFFFF3B30)
private val LINE_COLOR = Color(0xFF2196F3)
private val POINT_COLOR = Color(0xFF2196F3)

// Y轴范围提供器
private val CandlestickRangeProvider =
  object : CartesianLayerRangeProvider {
    override fun getMinY(minY: Double, maxY: Double, extraStore: ExtraStore) =
      Y_STEP * floor(minY / Y_STEP)

    override fun getMaxY(minY: Double, maxY: Double, extraStore: ExtraStore) =
      Y_STEP * ceil(maxY / Y_STEP)
  }

// 价格格式化
private val PriceFormatter = CartesianValueFormatter.decimal(DecimalFormat("#,###"))

// Y轴刻度间隔
private val StartAxisItemPlacer = VerticalAxis.ItemPlacer.step({ Y_STEP })

// X轴星期格式化
private val WeekdayFormatter =
  object : CartesianValueFormatter {
    private val weekdays = listOf("Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun")

    override fun format(
      context: CartesianMeasuringContext,
      value: Double,
      verticalAxisPosition: Axis.Position.Vertical?,
    ): String {
      val index = value.toInt()
      return if (index >= 0 && index < weekdays.size) weekdays[index] else ""
    }
  }

// Marker格式化
private val MarkerValueFormatter =
  DefaultCartesianMarker.ValueFormatter.default(DecimalFormat("#,###"))

@Composable
private fun JetpackComposeDailyCandlestickChart(
  modelProducer: CartesianChartModelProducer,
  modifier: Modifier = Modifier,
) {
  // 自定义红色蜡烛图
  val candleBody = rememberLineComponent(
    fill = fill(CANDLE_COLOR),
    thickness = 8.dp,
  )

  // 创建透明的烛芯组件，使其不可见
  val invisibleWick = rememberLineComponent(
    fill = fill(Color.Transparent),
    thickness = 0.dp,
  )

  val redCandle = CandlestickCartesianLayer.Candle(
    body = candleBody,
    topWick = invisibleWick,
    bottomWick = invisibleWick
  )

  // 创建一个始终返回红色蜡烛图的CandleProvider
  val candleProvider = object : CandlestickCartesianLayer.CandleProvider {
    override fun getCandle(
      entry: CandlestickCartesianLayerModel.Entry,
      extraStore: ExtraStore
    ): CandlestickCartesianLayer.Candle = redCandle

    override fun getWidestCandle(extraStore: ExtraStore): CandlestickCartesianLayer.Candle = redCandle
  }

  // 创建折线图组件
  val lineComponent = rememberLineComponent(
    fill = fill(LINE_COLOR),
    thickness = 3.dp,
  )

  // 创建折线图空心圆点组件
  val pointComponent = rememberShapeComponent(
    fill = fill(Color.White),  // 内部填充白色
    shape = CorneredShape.Pill,
    strokeFill = fill(LINE_COLOR),  // 边框使用线图颜色
    strokeThickness = 1.5.dp,  // 边框厚度
  )

  // 创建标记的文本组件
  val labelComponent = rememberTextComponent(
    color = Color.White,
    background = rememberShapeComponent(
      fill = fill(Color.DarkGray),
      shape = CorneredShape.Pill
    )
  )

  val materialTheme = MaterialTheme.colorScheme

  CartesianChartHost(
    rememberCartesianChart(
      rememberCandlestickCartesianLayer(
        candleProvider = candleProvider,
        minCandleBodyHeight = 2.dp,
        candleSpacing = 25.dp,
        scaleCandleWicks = false,
        rangeProvider = CandlestickRangeProvider
      ),
      rememberLineCartesianLayer(
        rangeProvider = CandlestickRangeProvider,
        lineProvider = LineCartesianLayer.LineProvider.series(
          listOf(
            LineCartesianLayer.rememberLine(
              fill = LineCartesianLayer.LineFill.single(fill(LINE_COLOR)),
              pointProvider = LineCartesianLayer.PointProvider.single(
                LineCartesianLayer.point(pointComponent, 8.dp)
              )
            )
          )
        )
      ),
      startAxis =
        VerticalAxis.rememberStart(
          valueFormatter = PriceFormatter,
          itemPlacer = StartAxisItemPlacer,
          label = null,
        ),
      bottomAxis =
        HorizontalAxis.rememberBottom(
          valueFormatter = WeekdayFormatter,
          tickLength = 0.dp,
        ),
      marker = DefaultCartesianMarker(
          label = labelComponent,
          valueFormatter = MarkerValueFormatter,
      ),
    ),
    modelProducer,
    modifier.height(300.dp),
  )
}

// X轴值，表示周一到周日
private val xValues = List(7) { it.toDouble() }

// 图片1中的蜡烛图数据，修改为新的趋势
// opening: 下降到中间然后上升趋势
private val opening = listOf(
  160.0, 140.0, 80.0, 60.0, 80.0, 140.0, 160.0
)

// closing: 上升到中间然后下降趋势
private val closing = listOf(
  60.0, 80.0, 120.0, 160.0, 120.0, 80.0, 60.0
)

// 将low设为opening和closing中的较小值
private val low = listOf(
  minOf(opening[0], closing[0]),
  minOf(opening[1], closing[1]),
  minOf(opening[2], closing[2]),
  minOf(opening[3], closing[3]),
  minOf(opening[4], closing[4]),
  minOf(opening[5], closing[5]),
  minOf(opening[6], closing[6])
)

// 将high设为opening和closing中的较大值
private val high = listOf(
  maxOf(opening[0], closing[0]),
  maxOf(opening[1], closing[1]),
  maxOf(opening[2], closing[2]),
  maxOf(opening[3], closing[3]),
  maxOf(opening[4], closing[4]),
  maxOf(opening[5], closing[5]),
  maxOf(opening[6], closing[6])
)

// 添加折线图数据 - 均线
private val lineData = listOf(
  110.0, 115.0, 105.0, 110.0, 100.0, 105.0, 110.0
)

@Composable
fun JetpackComposeDailyCandlestickChart(modifier: Modifier = Modifier) {
  val modelProducer = remember { CartesianChartModelProducer() }
  LaunchedEffect(Unit) {
    modelProducer.runTransaction {
      candlestickSeries(xValues, opening, closing, low, high)
      lineSeries { series(xValues, lineData) }
    }
  }
  JetpackComposeDailyCandlestickChart(modelProducer, modifier)
}

@Composable
@Preview
private fun Preview() {
  val modelProducer = remember { CartesianChartModelProducer() }
  // Use `runBlocking` only for previews, which don't support asynchronous execution.
  runBlocking {
    modelProducer.runTransaction {
      candlestickSeries(xValues, opening, closing, low, high)
      lineSeries { series(xValues, lineData) }
    }
  }
  PreviewBox { JetpackComposeDailyCandlestickChart(modelProducer) }
}
