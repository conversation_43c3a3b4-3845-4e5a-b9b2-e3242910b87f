package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@Composable
fun AscentWeeklyDemo(modifier: Modifier) {
    // 日期格式化工具
    val dateFormatter = remember { SimpleDateFormat("MM/dd", Locale.getDefault()) }

  var selectedX by remember { mutableStateOf<Long?>(20203) }


    // 创建上升高度周数据
    val chartData = remember {
        // 使用提供的确切数据
        val ascentSeries = ChartData.Series(
            chartType = ChartType.BAR,
            color = -11151487, // 使用提供的颜色值
            axisRange = ChartData.AxisRange(
                minX = 20199.0,
                maxX = 20205.0,
                minY = 0.0,
                maxY = 150.0
            ),
            entries = persistentListOf(
                ChartData.Entry(20200, 70.8),
                ChartData.Entry(20201, 80.6),
                ChartData.Entry(20203, 123.7),
                ChartData.Entry(20205, 54.5)
            ),
            value = buildAnnotatedString { append("330 米") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null
        )

        // 构建ChartData对象
        ChartData(
            chartContent = ChartContent.ASCENT,
            chartGranularity = ChartGranularity.WEEKLY,
            series = persistentListOf(ascentSeries),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.GROUPED,
            colorIndicator = null,
            selectEntryX = null // 使用状态变量控制选中点
        )
    }

    // 记录当前选中的项，初始值为20201

    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "上升高度周数据",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = "总上升高度: 330 米",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
        )

        // 显示选中的数据
        selectedX?.let { xValue ->
            val selectedEntry = chartData.series[0].entries.find { it.x == xValue }
            selectedEntry?.let { entry ->
                val dayLabel = when (xValue.toInt()) {
                    20200 -> "第200天"
                    20201 -> "第201天"
                    20203 -> "第203天"
                    20205 -> "第205天"
                    else -> "第${xValue}天"
                }

                Text(
                    text = "$dayLabel: ${entry.y} 米",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                )
            }
        }

        // 数据概览
        Text(
            text = "数据点: ${chartData.series[0].entries.size} 天",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
        )

        // 渲染图表
        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedX = x },
            onNoEntrySelected = { selectedX = null },
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
                .padding(16.dp)
        )

        // 数据详情
        Text(
            text = "数据详情:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        chartData.series[0].entries.forEach { entry ->
            val dayLabel = when (entry.x.toInt()) {
                20200 -> "第200天"
                20201 -> "第201天"
                20203 -> "第203天"
                20205 -> "第205天"
                else -> "第${entry.x.toInt()}天"
            }

            Text(
                text = "• $dayLabel: ${entry.y} 米",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }
    }
}
