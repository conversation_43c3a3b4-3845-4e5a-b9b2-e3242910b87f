package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.composeui.theme.cloudyGrey
import com.patrykandpatrick.vico.sample.stt.composeui.theme.vo2Max
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@Composable
fun HeartRateCandlestickDemo(modifier: Modifier) {
    // 日期格式化工具
    val dateFormatter = remember { SimpleDateFormat("MM/dd", Locale.getDefault()) }

    // 格式化具体日期
    fun formatDate(dayOfYear: Int): String {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.YEAR, 2024)
        calendar.set(Calendar.DAY_OF_YEAR, dayOfYear)
        return dateFormatter.format(calendar.time)
    }

    // 创建心率蜡烛图数据
    val chartData = remember {
        // 基于用户提供的测试数据
        val entries = persistentListOf(
            ChartData.Entry(20179, 68.56725180358218),
            ChartData.Entry(20180, 79.75641016776746),
            ChartData.Entry(20181, 76.71604920316625),
            ChartData.Entry(20182, 77.53968261537098),
            ChartData.Entry(20183, 88.22973035477303),
            ChartData.Entry(20184, 88.00000190734863),
            ChartData.Entry(20185, 83.58333420294981),
            ChartData.Entry(20186, 80.63934427792908),
            ChartData.Entry(20187, 66.7835823190746),
            ChartData.Entry(20188, 93.88571446282523),
            ChartData.Entry(20189, 88.57142840112958),
            ChartData.Entry(20190, 81.653060973907),
            ChartData.Entry(20191, 85.9285717351096),
            ChartData.Entry(20192, 66.63000000715256),
            ChartData.Entry(20193, 86.63888871669769),
            ChartData.Entry(20194, 100.47368476265355),
            ChartData.Entry(20195, 97.35294047523948),
            ChartData.Entry(20196, 72.79710137325785),
            ChartData.Entry(20197, 81.32539709409077),
            ChartData.Entry(20199, 83.5333331823349),
            ChartData.Entry(20200, 87.2542371184139),
            ChartData.Entry(20201, 67.83703676859538),
            ChartData.Entry(20202, 63.78787858919664),
            ChartData.Entry(20203, 79.25000008195639),
            ChartData.Entry(20204, 53.11940319502531),
            ChartData.Entry(20205, 69.00892856929984),
            ChartData.Entry(20206, 66.42857104539871),
            ChartData.Entry(20207, 80.68420999928524),
            ChartData.Entry(20208, 68.42857125827244)
        )

        val candlestickEntries = persistentListOf(
            ChartData.CandlestickEntry(20179, 47.000001668930054, 130.00000476837158, 47.000001668930054, 130.00000476837158),
            ChartData.CandlestickEntry(20180, 56.00000023841858, 120.0, 56.00000023841858, 120.0),
            ChartData.CandlestickEntry(20181, 55.000001192092896, 98.99999856948853, 55.000001192092896, 98.99999856948853),
            ChartData.CandlestickEntry(20182, 52.99999952316284, 104.00000095367432, 52.99999952316284, 104.00000095367432),
            ChartData.CandlestickEntry(20183, 64.00000333786011, 128.00000667572021, 64.00000333786011, 128.00000667572021),
            ChartData.CandlestickEntry(20184, 88.00000190734863, 88.00000190734863, 88.00000190734863, 88.00000190734863),
            ChartData.CandlestickEntry(20185, 56.00000023841858, 135.99999904632568, 56.00000023841858, 135.99999904632568),
            ChartData.CandlestickEntry(20186, 56.00000023841858, 126.99999332427979, 56.00000023841858, 126.99999332427979),
            ChartData.CandlestickEntry(20187, 48.99999976158142, 99.99999761581421, 48.99999976158142, 99.99999761581421),
            ChartData.CandlestickEntry(20188, 65.00000238418579, 144.0000057220459, 65.00000238418579, 144.0000057220459),
            ChartData.CandlestickEntry(20189, 59.000000953674316, 129.0000057220459, 59.000000953674316, 129.0000057220459),
            ChartData.CandlestickEntry(20190, 52.00000047683716, 135.99999904632568, 52.00000047683716, 135.99999904632568),
            ChartData.CandlestickEntry(20191, 52.99999952316284, 123.99999618530273, 52.99999952316284, 123.99999618530273),
            ChartData.CandlestickEntry(20192, 45.999999046325684, 135.0, 45.999999046325684, 135.0),
            ChartData.CandlestickEntry(20193, 62.99999713897705, 118.00000190734863, 62.99999713897705, 118.00000190734863),
            ChartData.CandlestickEntry(20194, 59.000000953674316, 167.99999713897705, 59.000000953674316, 167.99999713897705),
            ChartData.CandlestickEntry(20195, 67.00000047683716, 126.99999332427979, 67.00000047683716, 126.99999332427979),
            ChartData.CandlestickEntry(20196, 60.0, 96.00000143051147, 60.0, 96.00000143051147),
            ChartData.CandlestickEntry(20197, 41.99999928474426, 152.99999713897705, 41.99999928474426, 152.99999713897705),
            ChartData.CandlestickEntry(20199, 56.00000023841858, 126.99999332427979, 56.00000023841858, 126.99999332427979),
            ChartData.CandlestickEntry(20200, 57.999998331069946, 145.00000476837158, 57.999998331069946, 145.00000476837158),
            ChartData.CandlestickEntry(20201, 45.0, 125.9999942779541, 45.0, 125.9999942779541),
            ChartData.CandlestickEntry(20202, 45.0, 163.00000190734863, 45.0, 163.00000190734863),
            ChartData.CandlestickEntry(20203, 48.00000071525574, 146.00000381469727, 48.00000071525574, 146.00000381469727),
            ChartData.CandlestickEntry(20204, 40.000001192092896, 92.99999713897705, 40.000001192092896, 92.99999713897705),
            ChartData.CandlestickEntry(20205, 41.99999928474426, 132.00000286102295, 41.99999928474426, 132.00000286102295),
            ChartData.CandlestickEntry(20206, 52.99999952316284, 84.99999761581421, 52.99999952316284, 84.99999761581421),
            ChartData.CandlestickEntry(20207, 60.0, 98.99999856948853, 60.0, 98.99999856948853),
            ChartData.CandlestickEntry(20208, 49.999998807907104, 105.0, 49.999998807907104, 105.0)
        )

        // 创建心率蜡烛图系列
        val heartRateSeries = ChartData.Series(
            chartType = ChartType.CANDLESTICK,
            color =  Color(0xFF7E8084).toArgb(), // 用户提供的颜色值
            axisRange = ChartData.AxisRange(
                minX = 20179.0,
                maxX = 20208.0,
                minY = 40.0,
                maxY = 220.0
            ),
            entries = entries,
            value = buildAnnotatedString { append("40-168 次/分") },
            candlestickEntries = candlestickEntries,
            lineConfig = null,
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf(),
        )

        // 构建ChartData对象
        ChartData(
            chartContent = ChartContent.HEART_RATE,
            chartGranularity = ChartGranularity.MONTHLY,
            series = persistentListOf(heartRateSeries),
            highlightEnabled = true,
            goal = 89,
            highlightDecorationLines =
              persistentMapOf(165 to Color.Red.toArgb()),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            colorIndicator = null
        )
    }

    // 记录当前选中的项
    var selectedX by remember { mutableStateOf<Long?>(null) }

    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "心率蜡烛图（月度数据）",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = "显示心率的最高值、最低值和平均值变化趋势",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
        )

        // 显示选中的数据
        selectedX?.let { xValue ->
            val selectedEntry = chartData.series[0].entries.find { it.x == xValue }
            val selectedCandlestick = chartData.series[0].candlestickEntries?.find { it.x == xValue }

            selectedEntry?.let { entry ->
                selectedCandlestick?.let { candle ->
                    val date = formatDate(xValue.toInt())
                    Text(
                        text = "日期: $date",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
                    )
                    Text(
                        text = "平均心率: ${String.format("%.1f", entry.y)} 次/分",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
                    )
                    Text(
                        text = "最高: ${String.format("%.0f", candle.high)} 次/分, 最低: ${String.format("%.0f", candle.low)} 次/分",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
                    )
                    Text(
                        text = "开盘: ${String.format("%.0f", candle.open)} 次/分, 收盘: ${String.format("%.0f", candle.close)} 次/分",
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
                    )
                }
            }
        }

        // 渲染图表
        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedX = x },
            onNoEntrySelected = { selectedX = null },
            modifier = Modifier
                .fillMaxWidth()
                .height(350.dp)
                .padding(16.dp)
        )

        // 图表说明
        Text(
            text = "长按图表可查看详细数据。蜡烛图显示每日心率的开盘、收盘、最高和最低值。",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )
    }
}
