/*
 * Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.patrykandpatrick.vico.sample.compose

import androidx.compose.foundation.layout.height
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.compose.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberBottom
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberStart
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberCandlestickCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.rememberCartesianChart
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.core.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.core.cartesian.data.CartesianLayerRangeProvider
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.patrykandpatrick.vico.core.cartesian.data.candlestickSeries
import com.patrykandpatrick.vico.core.cartesian.marker.DefaultCartesianMarker
import com.patrykandpatrick.vico.core.common.data.ExtraStore
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import kotlin.math.ceil
import kotlin.math.floor
import kotlinx.coroutines.runBlocking

private const val Y_STEP = 5.0
private const val MS_IN_DAY = 86400000

private val RangeProvider =
  object : CartesianLayerRangeProvider {
    override fun getMinY(minY: Double, maxY: Double, extraStore: ExtraStore) =
      Y_STEP * floor(minY / Y_STEP)

    override fun getMaxY(minY: Double, maxY: Double, extraStore: ExtraStore) =
      Y_STEP * ceil(maxY / Y_STEP)
  }

private val StartAxisValueFormatter = CartesianValueFormatter.decimal(DecimalFormat("$#,###.00"))

private val StartAxisItemPlacer = VerticalAxis.ItemPlacer.step({ Y_STEP })

private val BottomAxisValueFormatter =
  object : CartesianValueFormatter {
    private val dateFormat = SimpleDateFormat("MM/dd", Locale.US)

    override fun format(
      context: CartesianMeasuringContext,
      value: Double,
      verticalAxisPosition: Axis.Position.Vertical?,
    ): String {
      val calendar = Calendar.getInstance()
      calendar.timeInMillis = value.toLong()
      return dateFormat.format(calendar.time)
    }
  }

private val MarkerValueFormatter =
  DefaultCartesianMarker.ValueFormatter.default(DecimalFormat("$#,###.00"))

@Composable
private fun JetpackComposeStockPriceChart(
  modelProducer: CartesianChartModelProducer,
  modifier: Modifier = Modifier,
) {
  val materialTheme = MaterialTheme.colorScheme
  
  CartesianChartHost(
    rememberCartesianChart(
      rememberCandlestickCartesianLayer(
        rangeProvider = RangeProvider,
        minCandleBodyHeight = 2.dp,
        candleSpacing = 5.dp,
        scaleCandleWicks = true
      ),
      startAxis =
        VerticalAxis.rememberStart(
          valueFormatter = StartAxisValueFormatter,
          itemPlacer = StartAxisItemPlacer,
        ),
      bottomAxis =
        HorizontalAxis.rememberBottom(
          guideline = null, 
          valueFormatter = BottomAxisValueFormatter
        ),
      marker = rememberMarker(valueFormatter = MarkerValueFormatter),
    ),
    modelProducer,
    modifier.height(280.dp),
  )
}

// 生成过去30天的日期时间戳（毫秒）
private val xValues = List(30) { index ->
  val calendar = Calendar.getInstance()
  calendar.add(Calendar.DAY_OF_MONTH, -30 + index)
  calendar.timeInMillis
}

// 模拟股票数据
private val opening = listOf(
  142.50, 143.20, 145.70, 146.80, 145.30, 144.10, 143.90, 145.20, 147.80, 148.50,
  149.30, 148.70, 150.10, 152.30, 151.80, 153.20, 154.70, 156.30, 155.90, 157.20,
  156.80, 154.30, 153.10, 151.80, 152.40, 153.70, 155.20, 156.90, 158.30, 157.50
)

private val closing = listOf(
  143.20, 145.70, 146.80, 145.30, 144.10, 143.90, 145.20, 147.80, 148.50, 149.30,
  148.70, 150.10, 152.30, 151.80, 153.20, 154.70, 156.30, 155.90, 157.20, 156.80,
  154.30, 153.10, 151.80, 152.40, 153.70, 155.20, 156.90, 158.30, 157.50, 159.80
)

private val low = listOf(
  141.30, 142.80, 144.90, 145.20, 143.70, 142.50, 143.10, 144.30, 146.90, 147.60,
  147.80, 148.10, 149.50, 150.70, 151.20, 152.30, 153.80, 155.10, 155.20, 156.30,
  153.90, 152.20, 150.90, 151.20, 151.50, 152.80, 154.30, 155.70, 157.10, 156.80
)

private val high = listOf(
  144.20, 146.50, 147.90, 148.10, 146.90, 145.70, 146.30, 148.40, 149.70, 150.80,
  150.90, 151.40, 153.20, 153.80, 154.50, 155.60, 157.20, 157.90, 158.30, 158.90,
  157.50, 155.80, 154.30, 153.90, 154.20, 155.90, 157.30, 158.40, 159.50, 160.70
)

@Composable
fun JetpackComposeStockPriceChart(modifier: Modifier = Modifier) {
  val modelProducer = remember { CartesianChartModelProducer() }
  LaunchedEffect(Unit) {
    modelProducer.runTransaction {
      candlestickSeries(xValues, opening, closing, low, high)
    }
  }
  JetpackComposeStockPriceChart(modelProducer, modifier)
}

@Composable
@Preview
private fun Preview() {
  val modelProducer = remember { CartesianChartModelProducer() }
  // Use `runBlocking` only for previews, which don't support asynchronous execution.
  runBlocking {
    modelProducer.runTransaction {
      candlestickSeries(xValues, opening, closing, low, high)
    }
  }
  PreviewBox { JetpackComposeStockPriceChart(modelProducer) }
} 