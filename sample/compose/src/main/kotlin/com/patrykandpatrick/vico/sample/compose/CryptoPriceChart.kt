/*
 * Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.patrykandpatrick.vico.sample.compose

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.patry<PERSON><PERSON><PERSON>.vico.compose.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberBottom
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberStart
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberCandlestickCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.rememberCartesianChart
import com.patrykandpatrick.vico.compose.common.component.rememberShapeComponent
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.core.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.core.cartesian.data.CartesianLayerRangeProvider
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.patrykandpatrick.vico.core.cartesian.data.candlestickSeries
import com.patrykandpatrick.vico.core.cartesian.marker.DefaultCartesianMarker
import com.patrykandpatrick.vico.core.common.data.ExtraStore
import com.patrykandpatrick.vico.core.common.shape.CorneredShape
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.math.ceil
import kotlin.math.floor
import kotlinx.coroutines.runBlocking

// 定义蜡烛图表的各种常量
private const val Y_STEP = 1000.0
private const val HOURS_IN_MS = 3600000

// 自定义颜色
private val BULLISH_COLOR = Color(0xFF4CAF50) // 上涨（看涨）颜色
private val BEARISH_COLOR = Color(0xFFF44336) // 下跌（看跌）颜色

// 自定义Y轴范围提供器，以确保图表显示合适的高度范围
private val RangeProvider =
  object : CartesianLayerRangeProvider {
    override fun getMinY(minY: Double, maxY: Double, extraStore: ExtraStore) =
      Y_STEP * floor(minY / Y_STEP)

    override fun getMaxY(minY: Double, maxY: Double, extraStore: ExtraStore) =
      Y_STEP * ceil(maxY / Y_STEP)
  }

// 设置价格格式（显示为美元）
private val PriceFormatter = CartesianValueFormatter.decimal(DecimalFormat("$#,###"))

// 设置Y轴刻度间隔为Y_STEP
private val StartAxisItemPlacer = VerticalAxis.ItemPlacer.step({ Y_STEP })

// 设置X轴时间格式为时:分
private val TimeFormatter =
  object : CartesianValueFormatter {
    private val dateFormat = SimpleDateFormat("HH:mm", Locale.US)

    override fun format(
      context: CartesianMeasuringContext,
      value: Double,
      verticalAxisPosition: Axis.Position.Vertical?,
    ): String {
      return dateFormat.format(Date(value.toLong()))
    }
  }

// 为Marker设置价格显示格式（详细到小数点后2位）
private val MarkerValueFormatter =
  DefaultCartesianMarker.ValueFormatter.default(DecimalFormat("$#,###.00"))

@Composable
private fun JetpackComposeCryptoPriceChart(
  modelProducer: CartesianChartModelProducer,
  modifier: Modifier = Modifier,
) {
  // 自定义烛体和烛线组件
  val candleBodyComponent = rememberShapeComponent(shape = CorneredShape.Pill)
  val materialTheme = MaterialTheme.colorScheme
  
  CartesianChartHost(
    rememberCartesianChart(
      rememberCandlestickCartesianLayer(
        minCandleBodyHeight = 2.dp,
        candleSpacing = 3.dp,
        scaleCandleWicks = true,
        rangeProvider = RangeProvider
      ),
      startAxis =
        VerticalAxis.rememberStart(
          valueFormatter = PriceFormatter,
          itemPlacer = StartAxisItemPlacer,
          label = null, // 移除Y轴标签
        ),
      bottomAxis =
        HorizontalAxis.rememberBottom(
          valueFormatter = TimeFormatter,
          tickLength = 4.dp,
        ),
      marker = rememberMarker(
          valueFormatter = MarkerValueFormatter,
          showIndicator = true,
      ),
    ),
    modelProducer,
    modifier.fillMaxWidth().height(300.dp),
  )
}

// 模拟24小时的加密货币价格数据（按小时）
private val xValues = List(24) { index ->
  // 从24小时前开始，每小时一个数据点
  System.currentTimeMillis() - (24 - index) * HOURS_IN_MS
}

// 模拟加密货币价格数据
private val opening = listOf(
  36250.0, 36420.0, 36380.0, 36520.0, 36780.0, 37120.0, 37350.0, 37210.0, 36980.0, 37450.0,
  37680.0, 37520.0, 37310.0, 37580.0, 37840.0, 38120.0, 38350.0, 38410.0, 38670.0, 38520.0,
  38280.0, 37980.0, 37820.0, 37650.0
)

private val closing = listOf(
  36420.0, 36380.0, 36520.0, 36780.0, 37120.0, 37350.0, 37210.0, 36980.0, 37450.0, 37680.0,
  37520.0, 37310.0, 37580.0, 37840.0, 38120.0, 38350.0, 38410.0, 38670.0, 38520.0, 38280.0,
  37980.0, 37820.0, 37650.0, 37920.0
)

private val low = listOf(
  36120.0, 36250.0, 36320.0, 36480.0, 36720.0, 36950.0, 37080.0, 36850.0, 36920.0, 37240.0,
  37350.0, 37150.0, 37280.0, 37420.0, 37780.0, 38050.0, 38280.0, 38350.0, 38420.0, 38180.0,
  37850.0, 37720.0, 37580.0, 37550.0
)

private val high = listOf(
  36580.0, 36520.0, 36650.0, 36920.0, 37250.0, 37480.0, 37450.0, 37250.0, 37580.0, 37850.0,
  37820.0, 37580.0, 37750.0, 38050.0, 38250.0, 38520.0, 38580.0, 38780.0, 38850.0, 38650.0,
  38320.0, 38150.0, 37950.0, 38120.0
)

@Composable
fun JetpackComposeCryptoPriceChart(modifier: Modifier = Modifier) {
  val modelProducer = remember { CartesianChartModelProducer() }
  LaunchedEffect(Unit) {
    modelProducer.runTransaction {
      candlestickSeries(xValues, opening, closing, low, high)
    }
  }
  JetpackComposeCryptoPriceChart(modelProducer, modifier)
}

@Composable
@Preview
private fun Preview() {
  val modelProducer = remember { CartesianChartModelProducer() }
  // Use `runBlocking` only for previews, which don't support asynchronous execution.
  runBlocking {
    modelProducer.runTransaction {
      candlestickSeries(xValues, opening, closing, low, high)
    }
  }
  PreviewBox { JetpackComposeCryptoPriceChart(modelProducer) }
} 