package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.time.LocalDate

@Composable
fun DurationSixMonthsDemo(modifier: Modifier) {
    val context = LocalContext.current
    
    // 日期映射工具 - 将epochDay转换为日期标签
    fun getDateLabel(x: Long): String {
        val targetDate = LocalDate.ofEpochDay(x)
        return "${targetDate.year}-${targetDate.monthValue}-${targetDate.dayOfMonth}"
    }

    // 创建基于提供数据的6个月持续时间数据
    val chartData = remember {
        val durationSeries = ChartData.Series(
            chartType = ChartType.BAR,
            color = -9643861, // 使用提供的颜色值
            axisRange = ChartData.AxisRange(
                minX = 19177.0,
                maxX = 19365.0,
                minY = 0.0,
                maxY = 360.0
            ),
            entries = persistentListOf(
                ChartData.Entry(19352, 6.1946666666666665)
            ),
            value = buildAnnotatedString { append("6 min") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
        )

        // 构建ChartData对象
        ChartData(
            chartContent = ChartContent.DURATION,
            chartGranularity = ChartGranularity.SIX_MONTHS,
            series = persistentListOf(durationSeries),
            highlightEnabled = true,
            goal = 180.0,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.GROUPED,
            colorIndicator = null,
            selectEntryX = null // 初始不选中任何项
        )
    }

    // 记录当前选中的项
    var selectedX by remember { mutableStateOf<Long?>(null) }

    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "6个月持续时间统计 ${getDateLabel(19177)}-${getDateLabel(19365)}",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = "当前持续时间: 6 分钟",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
        )

        // 显示目标值
        chartData.goal?.let { goal ->
            Text(
                text = "目标持续时间: ${goal} 分钟",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.secondary,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
            )
        }

        // 显示选中的数据
        selectedX?.let { xValue ->
            val selectedEntry = chartData.series[0].entries.find { it.x == xValue }
            selectedEntry?.let { entry ->
                val dateLabel = getDateLabel(xValue)
                val yValue = entry.y.toDouble()

                Text(
                    text = "$dateLabel: ${String.format("%.2f", yValue)}分钟",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                )
            }
        }

        // 数据概览
        Text(
            text = "数据点: ${chartData.series[0].entries.size} 个 | 数据范围: ${19365 - 19177 + 1}天",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
        )

        // 渲染图表
        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedX = x },
            onNoEntrySelected = { selectedX = null },
            modifier = Modifier
                .fillMaxWidth()
                .height(350.dp)
                .padding(start = 16.dp, top = 16.dp, bottom = 16.dp, end = 32.dp)
        )

        // 数据详情
        Text(
            text = "数据详情:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        chartData.series[0].entries.forEach { entry ->
            val dateLabel = getDateLabel(entry.x)
            val yValue = entry.y.toDouble()

            Text(
                text = "• $dateLabel: ${String.format("%.2f", yValue)}分钟",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        // 统计信息
        val avgDuration = chartData.series[0].entries.map { it.y.toDouble() }.average()
        val maxEntry = chartData.series[0].entries.maxByOrNull { it.y.toDouble() }
        val minEntry = chartData.series[0].entries.minByOrNull { it.y.toDouble() }

        Text(
            text = "统计信息:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        Text(
            text = "• 平均时长: ${String.format("%.2f", avgDuration)}分钟",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        maxEntry?.let { entry ->
            Text(
                text = "• 最长时间: ${getDateLabel(entry.x)} (${String.format("%.2f", entry.y.toDouble())}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        minEntry?.let { entry ->
            Text(
                text = "• 最短时间: ${getDateLabel(entry.x)} (${String.format("%.2f", entry.y.toDouble())}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        // 图表配置信息
        Text(
            text = "图表配置:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        Text(
            text = "• 图表类型: 柱状图 (BAR)",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        Text(
            text = "• 显示模式: 分组显示 (GROUPED)",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        Text(
            text = "• 高亮功能: ${if (chartData.highlightEnabled) "启用" else "禁用"}",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        Text(
            text = "• 坐标轴范围: X(${chartData.series[0].axisRange.minX}-${chartData.series[0].axisRange.maxX}), Y(${chartData.series[0].axisRange.minY}-${chartData.series[0].axisRange.maxY})",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )
    }
} 