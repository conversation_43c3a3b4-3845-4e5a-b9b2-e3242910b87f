package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@Composable
fun GroupedBarChartDemo(modifier: Modifier) {
    // 日期格式化工具
    val dateFormatter = remember { SimpleDateFormat("MM/dd", Locale.getDefault()) }

    // 格式化具体日期
    fun formatDate(dayOfYear: Int): String {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.YEAR, 2024)
        calendar.set(Calendar.DAY_OF_YEAR, dayOfYear)
        return dateFormatter.format(calendar.time)
    }

    // 创建分组柱状图数据
    val chartData = remember {
        // 设置X轴范围，1-7表示一周的七天
        val startX = 1.0
        val endX = 7.0

        // 创建第一组柱状图数据（步数）
        val steps = ChartData.Series(
            chartType = ChartType.BAR,
            color = Color(0xFF4285F4).toArgb(), // 蓝色
            axisRange = ChartData.AxisRange(
                minX = startX,
                maxX = endX,
                minY = 0.0,
                maxY = 1500.0
            ),
            entries = persistentListOf(
                ChartData.Entry(1, 753),
                ChartData.Entry(2, 1245),
                ChartData.Entry(3, 532),
                ChartData.Entry(4, 123),
                ChartData.Entry(5, 1024),
                ChartData.Entry(6, 654),
                ChartData.Entry(7, 521)
            ),
            value = buildAnnotatedString { append("步数") },
            candlestickEntries = null,
            lineConfig = null,
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        )

        // 创建第二组柱状图数据（卡路里）
        val calories = ChartData.Series(
            chartType = ChartType.BAR,
            color = Color(0xFFEA4335).toArgb(), // 红色
            axisRange = ChartData.AxisRange(
                minX = startX,
                maxX = endX,
                minY = 0.0,
                maxY = 600.0
            ),
            entries = persistentListOf(
                ChartData.Entry(1, 345),
                ChartData.Entry(2, 412),
                ChartData.Entry(3, 289),
                ChartData.Entry(4, 378),
                ChartData.Entry(5, 465),
                ChartData.Entry(6, 256),
                ChartData.Entry(7, 210)
            ),
            value = buildAnnotatedString { append("卡路里") },
            candlestickEntries = null,
            lineConfig = null,
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        )

        // 构建ChartData对象，设置为GROUPED分组模式
        ChartData(
            chartContent = ChartContent.STEPS,
            chartGranularity = ChartGranularity.WEEKLY,
            series = persistentListOf(steps, calories),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.GROUPED,
            colorIndicator = null
        )
    }

    // 记录当前选中的项
    var selectedX by remember { mutableStateOf<Long?>(null) }

    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "步数与卡路里分组柱状图",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        // 显示选中的数据
        selectedX?.let { xValue ->
            val dayOfWeek = when (xValue.toInt()) {
                1 -> "周一"
                2 -> "周二"
                3 -> "周三"
                4 -> "周四"
                5 -> "周五"
                6 -> "周六"
                7 -> "周日"
                else -> "未知"
            }

            val stepsValue = chartData.series[0].entries.find { it.x == xValue }?.y?.toString() ?: "-"
            val caloriesValue = chartData.series[1].entries.find { it.x == xValue }?.y?.toString() ?: "-"

            Text(
                text = "$dayOfWeek: 步数 $stepsValue 步, 卡路里 $caloriesValue 千卡",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }

        // 渲染图表
        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedX = x },
            onNoEntrySelected = { selectedX = null },
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
                .padding(16.dp)
        )
    }
}
