/*
 * Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.patrykandpatrick.vico.sample.compose

import androidx.compose.foundation.layout.height
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.compose.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberBottom
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberEnd
import com.patrykandpatrick.vico.compose.cartesian.axis.rememberStart
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberCandlestickCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.layer.rememberColumnCartesianLayer
import com.patrykandpatrick.vico.compose.cartesian.rememberCartesianChart
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.axis.HorizontalAxis
import com.patrykandpatrick.vico.core.cartesian.axis.VerticalAxis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.core.cartesian.data.CartesianLayerRangeProvider
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.patrykandpatrick.vico.core.cartesian.data.candlestickSeries
import com.patrykandpatrick.vico.core.cartesian.data.columnSeries
import com.patrykandpatrick.vico.core.cartesian.marker.DefaultCartesianMarker
import com.patrykandpatrick.vico.core.common.data.ExtraStore
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.math.ceil
import kotlin.math.floor
import kotlinx.coroutines.runBlocking

// 定义图表常量
private const val PRICE_Y_STEP = 5.0
private const val VOLUME_Y_STEP = 10000.0
private const val HOURS_IN_MS = 3600000

// 自定义颜色
private val UP_COLOR = Color(0xFF26A69A) // 上涨颜色
private val DOWN_COLOR = Color(0xFFEF5350) // 下跌颜色

// 两个Y轴的范围提供器
private val PriceRangeProvider =
  object : CartesianLayerRangeProvider {
    override fun getMinY(minY: Double, maxY: Double, extraStore: ExtraStore) =
      PRICE_Y_STEP * floor(minY / PRICE_Y_STEP)

    override fun getMaxY(minY: Double, maxY: Double, extraStore: ExtraStore) =
      PRICE_Y_STEP * ceil(maxY / PRICE_Y_STEP)
  }

private val VolumeRangeProvider =
  object : CartesianLayerRangeProvider {
    override fun getMinY(minY: Double, maxY: Double, extraStore: ExtraStore) = 0.0

    override fun getMaxY(minY: Double, maxY: Double, extraStore: ExtraStore) =
      VOLUME_Y_STEP * ceil(maxY / VOLUME_Y_STEP)
  }

// 自定义格式化
private val PriceFormatter = CartesianValueFormatter.decimal(DecimalFormat("$#,###.00"))

private val VolumeFormatter = object : CartesianValueFormatter {
  private val decimalFormat = DecimalFormat("#,###")
  
  override fun format(
    context: CartesianMeasuringContext,
    value: Double,
    verticalAxisPosition: Axis.Position.Vertical?,
  ): String {
    return when {
      value >= 1_000_000 -> "${decimalFormat.format(value / 1_000_000)}M"
      value >= 1_000 -> "${decimalFormat.format(value / 1_000)}K"
      else -> decimalFormat.format(value)
    }
  }
}

// X轴日期格式化
private val DateFormatter =
  object : CartesianValueFormatter {
    private val dateFormat = SimpleDateFormat("MM/dd", Locale.US)

    override fun format(
      context: CartesianMeasuringContext,
      value: Double,
      verticalAxisPosition: Axis.Position.Vertical?,
    ): String {
      return dateFormat.format(Date(value.toLong()))
    }
  }

@Composable
private fun JetpackComposeCandlestickWithVolumeChart(
  modelProducer: CartesianChartModelProducer,
  modifier: Modifier = Modifier,
) {
  // 创建蜡烛图层
  val candlestickLayer = rememberCandlestickCartesianLayer(
    minCandleBodyHeight = 2.dp,
    candleSpacing = 4.dp,
    scaleCandleWicks = true,
    rangeProvider = PriceRangeProvider,
    verticalAxisPosition = Axis.Position.Vertical.Start
  )
  
  // 创建柱状图层（成交量）
  val volumeLayer = rememberColumnCartesianLayer(
    rangeProvider = VolumeRangeProvider,
    verticalAxisPosition = Axis.Position.Vertical.End,
  )
  
  CartesianChartHost(
    rememberCartesianChart(
      volumeLayer, 
      candlestickLayer,
      startAxis =
        VerticalAxis.rememberStart(
          valueFormatter = PriceFormatter,
          itemPlacer = VerticalAxis.ItemPlacer.step({ PRICE_Y_STEP }),
          title = "价格"
        ),
      endAxis =
        VerticalAxis.rememberEnd(
          valueFormatter = VolumeFormatter,
          itemPlacer = VerticalAxis.ItemPlacer.step({ VOLUME_Y_STEP }),
          title = "成交量"
        ),
      bottomAxis =
        HorizontalAxis.rememberBottom(
          valueFormatter = DateFormatter,
          tickLength = 4.dp,
        ),
      marker = rememberMarker(valueFormatter = DefaultCartesianMarker.ValueFormatter.default()),
    ),
    modelProducer,
    modifier.height(350.dp),
  )
}

// X轴日期（毫秒时间戳）
private val xValues = List(10) { index ->
  // 最近10天的数据，每天一个点
  System.currentTimeMillis() - (10 - index) * 24 * HOURS_IN_MS
}

// 价格数据
private val opening = listOf(145.30, 146.80, 148.20, 147.60, 149.10, 151.40, 150.80, 152.30, 153.60, 155.20)
private val closing = listOf(146.80, 148.20, 147.60, 149.10, 151.40, 150.80, 152.30, 153.60, 155.20, 154.70)
private val low = listOf(144.80, 146.20, 146.90, 147.10, 148.50, 150.10, 150.10, 151.70, 153.00, 154.10)
private val high = listOf(147.30, 149.10, 148.70, 150.20, 152.00, 152.20, 153.40, 154.50, 156.10, 156.20)

// 成交量数据
private val volume = listOf(35420.0, 42180.0, 38560.0, 41230.0, 45780.0, 38940.0, 42670.0, 47520.0, 51230.0, 48760.0)

@Composable
fun JetpackComposeCandlestickWithVolumeChart(modifier: Modifier = Modifier) {
  val modelProducer = remember { CartesianChartModelProducer() }
  LaunchedEffect(Unit) {
    modelProducer.runTransaction {
      candlestickSeries(xValues, opening, closing, low, high)
      columnSeries {
        series(xValues, volume)
      }
    }
  }
  JetpackComposeCandlestickWithVolumeChart(modelProducer, modifier)
}

@Composable
@Preview
private fun Preview() {
  val modelProducer = remember { CartesianChartModelProducer() }
  // Use `runBlocking` only for previews, which don't support asynchronous execution.
  runBlocking {
    modelProducer.runTransaction {
      candlestickSeries(xValues, opening, closing, low, high)
      columnSeries {
        series(xValues, volume)
      }
    }
  }
  PreviewBox { JetpackComposeCandlestickWithVolumeChart(modelProducer) }
} 