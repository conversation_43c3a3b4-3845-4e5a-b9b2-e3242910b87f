package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart

import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.time.LocalDate
import java.time.temporal.ChronoUnit

@Composable
fun DurationMonthlyDemo2(modifier: Modifier) {
    // 日期映射工具 - 将epochDay转换为日期标签
    fun getDateLabel(x: Long): String {
        val targetDate = LocalDate.ofEpochDay(x)
        return "${targetDate.monthValue}/${targetDate.dayOfMonth}"
    }

    // 创建基于用户提供数据的月度持续时间数据
    val chartData = remember {
        val durationSeries = ChartData.Series(
            chartType = ChartType.BAR,
            color = -9643861, // 使用提供的颜色值
            axisRange = ChartData.AxisRange(
                minX = 20270.0,
                maxX = 20300.0,
                minY = 0.0,
                maxY = 360.0
            ),
            entries = persistentListOf(
                ChartData.Entry(20271, 2.183333333333333),
                ChartData.Entry(20277, 95.708),
                ChartData.Entry(20278, 92.40816666666666),
                ChartData.Entry(20279, 7.3375),
                ChartData.Entry(20282, 42.758500000000005),
                ChartData.Entry(20284, 181.5787333333333),
                ChartData.Entry(20285, 73.81998333333333),
                ChartData.Entry(20287, 162.16146666666663),
                ChartData.Entry(20291, 155.3885),
                ChartData.Entry(20292, 75.19235),
                ChartData.Entry(20294, 160.30496666666667)
            ),
            value = buildAnnotatedString { append("17 小时 29 分") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
        )

        // 构建ChartData对象
        ChartData(
            chartContent = ChartContent.DURATION,
            chartGranularity = ChartGranularity.MONTHLY,
            series = persistentListOf(durationSeries),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.GROUPED,
            colorIndicator = null,
            selectEntryX = null // 初始不选中任何项
        )
    }

    // 记录当前选中的项
    var selectedX by remember { mutableStateOf<Long?>(null) }

    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "月度持续时间统计 (30天数据)"+getDateLabel(20270) + "-" +getDateLabel(20300) + " [测试版]",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = "总持续时间: 17 小时 29 分",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
        )

        // 显示选中的数据
        selectedX?.let { xValue ->
            val selectedEntry = chartData.series[0].entries.find { it.x == xValue }
            selectedEntry?.let { entry ->
                val dateLabel = getDateLabel(xValue)
                val yValue = entry.y.toDouble()
                val hours = (yValue / 60.0).toInt()
                val minutes = (yValue % 60.0).toInt()

                Text(
                    text = "$dateLabel: ${hours}小时${minutes}分钟 (${String.format("%.1f", yValue)}分钟)",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                )
            }
        }

        // 数据概览
        Text(
            text = "数据点: ${chartData.series[0].entries.size} 天 | 数据范围: 30天",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
        )



        // 渲染图表
        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedX = x },
            onNoEntrySelected = { selectedX = null },
            modifier = Modifier
                .fillMaxWidth()
                .height(350.dp)
                .padding(start = 16.dp, top = 16.dp, bottom = 16.dp, end = 32.dp)
        )

        // 数据详情
        Text(
            text = "数据详情:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        chartData.series[0].entries.forEach { entry ->
            val dateLabel = getDateLabel(entry.x)
            val yValue = entry.y.toDouble()
            val hours = (yValue / 60.0).toInt()
            val minutes = (yValue % 60.0).toInt()

            Text(
                text = "• $dateLabel: ${hours}小时${minutes}分钟",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        // 统计信息
        val avgDuration = chartData.series[0].entries.map { it.y.toDouble() }.average()
        val maxEntry = chartData.series[0].entries.maxByOrNull { it.y.toDouble() }
        val minEntry = chartData.series[0].entries.minByOrNull { it.y.toDouble() }

        Text(
            text = "统计信息:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        Text(
            text = "• 平均时长: ${String.format("%.1f", avgDuration)}分钟",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        maxEntry?.let { entry ->
            Text(
                text = "• 最长时间: ${getDateLabel(entry.x)} (${String.format("%.1f", entry.y.toDouble())}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        minEntry?.let { entry ->
            Text(
                text = "• 最短时间: ${getDateLabel(entry.x)} (${String.format("%.1f", entry.y.toDouble())}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }
    }
}
