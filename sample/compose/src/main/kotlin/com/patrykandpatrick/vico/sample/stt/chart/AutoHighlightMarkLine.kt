package com.patrykandpatrick.vico.sample.stt.chart

import com.patrykandpatrick.vico.core.cartesian.CartesianDrawingContext
import com.patrykandpatrick.vico.core.cartesian.decoration.Decoration
import com.patrykandpatrick.vico.core.common.component.LineComponent
import com.patrykandpatrick.vico.core.common.data.ExtraStore

/**
 * A [Decoration] that highlights an _x_ value with a vertical line.
 *
 * @property x returns the _x_ value.
 * @property line the [LineComponent] for the vertical line.
 */
internal class AutoHighlightMarkLine(
    private val x: (ExtraStore) -> Double,
    private val line: LineComponent,
) : Decoration {

    override fun drawOverLayers(context: CartesianDrawingContext) {
        with(context) {
            val xValue = x(model.extraStore)

            // 使用与HorizontalAxis相同的计算方式
            val layerStart = if (isLtr) layerBounds.left else layerBounds.right
            val baseCanvasX = layerStart - scroll +
                layerDimensions.startPadding * layoutDirectionMultiplier

            val canvasX = baseCanvasX +
                ((xValue - ranges.minX) / ranges.xStep).toFloat() *
                layerDimensions.xSpacing *
                layoutDirectionMultiplier

            // 绘制垂直线，使用与HighlightMarker相同的范围
            line.drawVertical(
                context = context,
                x = canvasX,
                top = 0.0F,
                bottom = layerBounds.bottom
            )
        }
    }
}
