package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@Composable
fun VO2MaxDemo(modifier: Modifier) {
    // 日期格式化工具
    val dateFormatter = remember { SimpleDateFormat("yyyy/MM/dd", Locale.getDefault()) }

    // 格式化具体日期
    fun formatDate(timestamp: Long): String {
        // 数据X轴时间戳是使用基于2024年的日期序号
        // 例如: 20129 表示从2024年1月1日起的第129天，即2024年5月8日
        // 创建一个2024年1月1日的基准日期
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 1, 0, 0, 0)
        calendar.set(Calendar.MILLISECOND, 0)

        // 计算目标日期 = 基准日期 + (timestamp - 20000) 天
        // 20000 是一个参考点，对应2024年的某一天
        val daysToAdd = timestamp - 20000
        calendar.add(Calendar.DAY_OF_YEAR, daysToAdd.toInt())

        // 格式化日期为 yyyy/MM/dd
        return dateFormatter.format(calendar.time)
    }

    // 创建VO2MAX图表数据
    val chartData = remember {
        // 设置180天的X轴范围，起始值是20039，确保能均分8个标签
        val startX = 20039.0 // 起始日期
        val endX = 20218.0 // 结束日期 = 起始日期 + 7个间隔(每个间隔21天)

        // 创建11个Series
        val seriesList = mutableListOf<ChartData.Series>()

        // Series 1 - 包含 x=20129,20130 的渐变条目
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741, // 蓝色
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = false,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20129,
                    y = 50.5f,
                    color = -17152
                ),
                ChartData.GradientLineEntry(
                    x = 20130,
                    y = 50.5f,
                    color = -17152
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 2 - 包含 x=20179,20180,20181 的渐变条目
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = false,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20179,
                    y = 50.599998474121094f,
                    color = -17152
                ),
                ChartData.GradientLineEntry(
                    x = 20180,
                    y = 49.900001525878906f,
                    color = -17152
                ),
                ChartData.GradientLineEntry(
                    x = 20181,
                    y = 49.900001525878906f,
                    color = -17152
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 3 - 单点 x=20186
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20186,
                    y = 60.20000076293945f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 4 - 单点 x=20191
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20191,
                    y = 60.20000076293945f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 5 - 单点 x=20195
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20195,
                    y = 60.20000076293945f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 6 - 单点 x=20198
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20198,
                    y = 58.599998474121094f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 7 - 两点 x=20200,20201
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = false,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20200,
                    y = 58.599998474121094f,
                    color = -15031741
                ),
                ChartData.GradientLineEntry(
                    x = 20201,
                    y = 58.900001525878906f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 8 - 单点 x=20203
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20203,
                    y = 58.900001525878906f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 9 - 两点 x=20205,20206
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = false,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20205,
                    y = 58.900001525878906f,
                    color = -15031741
                ),
                ChartData.GradientLineEntry(
                    x = 20206,
                    y = 58.900001525878906f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 10 - 单点 x=20212
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20212,
                    y = 58.900001525878906f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 11 - 单点 x=20216
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = startX.toDouble(),
                maxX = endX.toDouble(),
                minY = 32.0,
                maxY = 65.0
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20216,
                    y = 59.099998474121094f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        ChartData(
            chartContent = ChartContent.VO2MAX,
            chartGranularity = ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS,
            series = persistentListOf(*seriesList.toTypedArray()),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            colorIndicator = ChartData.ColorIndicator(
                thresholds = persistentListOf(32, 42, 55, 65),
                colors = persistentListOf(-52429, -17152, -15031741), // 红、黄、蓝
                labelTexts = persistentListOf(),
                barThicknessDp = 8.0f,
                barShiftXDp = 0.0f,
                useThresholdsAsAxisLines = true
            )
        )
    }

    // 收集所有的数据点，用于查找选中数据
    val allDataPoints = remember {
        chartData.series.flatMap { series ->
            series.gradientEntries.map { entry ->
                Triple(entry.x, entry.y.toFloat(), series.value)
            }
        }
    }

    var selectedEntryX by remember { mutableStateOf<Long?>(null) }

    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "VO₂ Max 图表",
                style = MaterialTheme.typography.titleMedium,
                modifier = Modifier.weight(1f)
            )

            selectedEntryX?.let { x ->
                val point = allDataPoints.find { it.first == x }
                point?.let {
                    Text(
                        text = "日期: ${formatDate(x)}\nVO₂ Max: ${it.second}",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
        }

        // 使用Chart组件显示图表
        Chart(
            chartData = chartData,
            onEntrySelected = { entryX ->
                selectedEntryX = entryX
            },
            onNoEntrySelected = {
                selectedEntryX = null
            },
            modifier = Modifier
                .fillMaxWidth()
        )

        Text(
            text = selectedEntryX?.let {
                val point = allDataPoints.find { it.first == selectedEntryX }
                point?.third?.toString() ?: "长按图表查看详细数据"
            } ?: "长按图表查看详细数据",
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.padding(top = 8.dp)
        )
    }
}
