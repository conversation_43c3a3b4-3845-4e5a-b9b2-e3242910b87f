package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@Composable
fun VO2MaxYearlyDemo(modifier: Modifier) {
    // 日期格式化工具
    val dateFormatter = remember { SimpleDateFormat("yyyy/MM/dd", Locale.getDefault()) }

    // 格式化具体日期
    fun formatDate(timestamp: Long): String {
        // 数据X轴时间戳是使用基于2024年的日期序号
        // 例如: 20129 表示从2024年1月1日起的第129天，即2024年5月8日
        // 创建一个2024年1月1日的基准日期
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 1, 0, 0, 0)
        calendar.set(Calendar.MILLISECOND, 0)

        // 计算目标日期 = 基准日期 + (timestamp - 20000) 天
        // 20000 是一个参考点，对应2024年的某一天
        val daysToAdd = timestamp - 20000
        calendar.add(Calendar.DAY_OF_YEAR, daysToAdd.toInt())

        // 格式化日期为 yyyy/MM/dd
        return dateFormatter.format(calendar.time)
    }

    // 创建VO2MAX年度图表数据
    val chartData = remember {
        // 使用用户提供的数据范围
        val minX = 19854.0
        val maxX = 20218.0
        val minY = 32.0
        val maxY = 65.0

        // 创建所有提供的Series数据
        val seriesList = mutableListOf<ChartData.Series>()

        // Series 1
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741, // 蓝色
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = false,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20129,
                    y = 50.5f,
                    color = -17152
                ),
                ChartData.GradientLineEntry(
                    x = 20130,
                    y = 50.5f,
                    color = -17152
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 2
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = false,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20179,
                    y = 50.599998474121094f,
                    color = -17152
                ),
                ChartData.GradientLineEntry(
                    x = 20180,
                    y = 49.900001525878906f,
                    color = -17152
                ),
                ChartData.GradientLineEntry(
                    x = 20181,
                    y = 49.900001525878906f,
                    color = -17152
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 3
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20186,
                    y = 60.20000076293945f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 4
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20191,
                    y = 60.20000076293945f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 5
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20195,
                    y = 60.20000076293945f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 6
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20198,
                    y = 58.599998474121094f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 7
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = false,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20200,
                    y = 58.599998474121094f,
                    color = -15031741
                ),
                ChartData.GradientLineEntry(
                    x = 20201,
                    y = 58.900001525878906f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 8
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20203,
                    y = 58.900001525878906f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 9
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = false,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20205,
                    y = 58.900001525878906f,
                    color = -15031741
                ),
                ChartData.GradientLineEntry(
                    x = 20206,
                    y = 58.900001525878906f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 10
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20212,
                    y = 58.900001525878906f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // Series 11
        seriesList.add(ChartData.Series(
            chartType = ChartType.LINE,
            color = -15031741,
            axisRange = ChartData.AxisRange(
                minX = minX,
                maxX = maxX,
                minY = minY,
                maxY = maxY
            ),
            entries = persistentListOf(),
            value = buildAnnotatedString { append("56.2 - 极好") },
            candlestickEntries = null,
            lineConfig = LineChartConfig(
                isSmoothCurve = false,
                showPoints = true,
                pointSizeDP = 8.0f,
                isPointFilled = false,
                showAreaFill = false,
                areaAlpha = 0.0f,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(
                ChartData.GradientLineEntry(
                    x = 20216,
                    y = 59.099998474121094f,
                    color = -15031741
                )
            ),
            backgroundRegion = null,
            groupStackBarStyle = null,
            groupStackBarEntries = persistentListOf()
        ))

        // 创建完整的ChartData
        ChartData(
            chartContent = ChartContent.VO2MAX,
            chartGranularity = ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS,
            series = persistentListOf(*seriesList.toTypedArray()),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            colorIndicator = ChartData.ColorIndicator(
                thresholds = persistentListOf(32, 42, 55, 65),
                colors = persistentListOf(-52429, -17152, -15031741),
                labelTexts = persistentListOf(),
                barThicknessDp = 8.0f,
                barShiftXDp = 0.0f,
                useThresholdsAsAxisLines = true
            )
        )
    }

    var selectedEntryX by remember { mutableStateOf<Long?>(null) }
    val selectedEntry = selectedEntryX?.let { x ->
        // 在所有series中找到对应x值的点
        chartData.series.flatMap { series ->
            series.gradientEntries.filter { it.x == x }
        }.firstOrNull()
    }

    Column(modifier = modifier.padding(16.dp)) {
        Text(
            text = "VO₂Max 年度数据",
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "当前值: ",
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(end = 4.dp)
            )
            Text(
                text = "56.2 - 极好",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.primary
            )
        }

        selectedEntry?.let {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "选中日期: ${formatDate(it.x)}",
                    style = MaterialTheme.typography.bodyMedium,
                    modifier = Modifier.padding(end = 8.dp)
                )
                Text(
                    text = "VO₂Max值: ${it.y}",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }

        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedEntryX = x },
            onNoEntrySelected = { selectedEntryX = null },
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 16.dp)
        )

        Text(
            text = "VO₂Max (最大摄氧量) 用于评估有氧适能和运动表现的关键指标。较高的VO₂Max值表示更好的有氧体能水平和心肺功能。图表中的颜色区域表示不同的健康水平范围。",
            style = MaterialTheme.typography.bodySmall,
            modifier = Modifier.padding(top = 16.dp)
        )
    }
}
