package com.stt.android.chart.impl.chart.axis

import android.content.Context
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.patrykandpatrick.vico.sample.compose.R
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.model.firstDayOfEpochMonth
import com.patrykandpatrick.vico.sample.stt.utils.TextFormatter
import com.patrykandpatrick.vico.sample.stt.utils.toEpochMilli
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter
import kotlin.math.roundToInt
import kotlin.math.roundToLong

internal fun createXAxisValueFormatter(
  context: Context,
  chartGranularity: ChartGranularity,
): CartesianValueFormatter = when (chartGranularity) {
    ChartGranularity.DAILY -> HourMinuteValueFormatter()
    ChartGranularity.WEEKLY,
    ChartGranularity.SEVEN_DAYS -> WeekdayValueFormatter()
    ChartGranularity.MONTHLY,
    ChartGranularity.THIRTY_DAYS -> DateValueFormatter()
    ChartGranularity.SIXTY_DAYS,
    ChartGranularity.SIX_WEEKS,
    ChartGranularity.ONE_HUNDRED_EIGHTY_DAYS -> MonthDateValueFormatter(context)
    ChartGranularity.SIX_MONTHS -> DynamicMonthValueFormatter() // 使用新的动态格式化器
    ChartGranularity.THREE_HUNDRED_SIXTY_FIVE_DAYS -> YearlyMonthDayValueFormatter(context)
    ChartGranularity.YEARLY -> ShortestMonthValueFormatter(context)
    ChartGranularity.EIGHT_YEARS -> YearValueFormatter()
}

private class HourMinuteValueFormatter : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence = buildString {
        val minutesSinceEpoch = value.roundToLong()
        val localDateTime = LocalDateTime.ofEpochSecond(minutesSinceEpoch * 60L, 0, OffsetDateTime.now().offset)
        val hours = localDateTime.hour
        val minutes = localDateTime.minute
        if (hours < 10) {
            append('0')
        }
        append(hours)
        append(':')
        if (minutes < 10) {
            append('0')
        }
        append(minutes)
    }
}

private class WeekdayValueFormatter : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        return LocalDate.ofEpochDay(epochDay)
            .format(DateTimeFormatter.ofPattern("E"))
    }
}

private class DateValueFormatter : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
      val epochDay = value.roundToLong()
      return LocalDate.ofEpochDay(epochDay)
        .format(DateTimeFormatter.ofPattern("M/d"))
    }
}

private class MonthDateValueFormatter(private val context: Context) : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        val millisSinceEpoch = LocalDate.ofEpochDay(epochDay)
            .atStartOfDay()
            .toEpochMilli()
        return TextFormatter.formatDate(this.context, millisSinceEpoch, false)
    }
}

private class ShortMonthValueFormatter(private val context: Context) : CartesianValueFormatter {
    private var previousMonth: Int = -1

    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        val month = LocalDate.ofEpochDay(epochDay).monthValue
        return if (month != previousMonth) {
            previousMonth = month
            this.context.resources.getStringArray(R.array.abbreviated_months)[month - 1]
        } else {
            " "
        }
    }
}

private class YearlyMonthDayValueFormatter(val context: Context) : CartesianValueFormatter {

    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        val millisSinceEpoch = LocalDate.ofEpochDay(epochDay)
            .atStartOfDay()
            .toEpochMilli()
        return TextFormatter.formatDate(this.context, millisSinceEpoch, true)
    }
}

private class ShortestMonthValueFormatter(private val context: Context) : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochMonth = value.roundToInt()
        val month = firstDayOfEpochMonth(epochMonth).monthValue
        return this.context.resources.getStringArray(R.array.abbreviated_months_shortest)[month - 1]
    }
}

private class YearValueFormatter : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val year = value.roundToInt()
        return year.toString()
    }
}

// 动态月份值格式化器 - 根据实际数据范围智能格式化月份标签
private class DynamicMonthValueFormatter : CartesianValueFormatter {
    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        // 根据数据的转换逻辑：实际日期 = 2024-01-01 + (epochDay - 20000) 天
        val epochDay = value.toLong()
        val baseDate = LocalDate.of(2024, 1, 1)
        val actualDate = baseDate.plusDays(epochDay - 20000)
        
        val month = actualDate.monthValue
        return "${month}月"
    }
}
