package com.patrykandpatrick.vico.sample.stt.chart

import android.annotation.SuppressLint
import android.graphics.Paint
import android.graphics.Path
import com.patrykandpatrick.vico.core.common.DrawingContext
import com.patrykandpatrick.vico.core.common.Fill
import com.patrykandpatrick.vico.core.common.Insets
import com.patrykandpatrick.vico.core.common.component.LineComponent

import com.patrykandpatrick.vico.core.common.shape.Shape

/**
 * 带有顶部倒三角形装饰的自定义LineComponent
 *
 * @param triangleSize 三角形的大小（宽度和高度，单位dp）
 * @param triangleFill 三角形的填充色，如果为null则使用线条颜色
 */
class LineComponentWithTriangle(
    fill: Fill,
    thicknessDp: Float = 1f,
    shape: Shape = Shape.Rectangle,
    margins: Insets = Insets.Zero,
    strokeFill: Fill = Fill.Transparent,
    strokeThicknessDp: Float = 0f,
    private val triangleSize: Float = 4f, // 三角形大小（dp）
    private val triangleFill: Fill? = null // 三角形填充色，null时使用线条颜色
) : LineComponent(fill, thicknessDp, shape, margins, strokeFill, strokeThicknessDp) {

    private val trianglePaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
        color = (triangleFill ?: fill).color
    }

    private val trianglePath = Path()

    override fun drawVertical(
        context: DrawingContext,
        x: Float,
        top: Float,
        bottom: Float,
        thicknessFactor: Float
    ) {
        // 先绘制原始的垂直线
        super.drawVertical(context, x, top, bottom, thicknessFactor)

        // 然后在顶部绘制倒三角形
        drawTriangleAtTop(context, x, top)
    }

    private fun drawTriangleAtTop(context: DrawingContext, x: Float, top: Float) {
        with(context) {
            val triangleSizePx = triangleSize.pixels
            val halfTriangleWidth = triangleSizePx / 2f
            val offsetDp = 0f // 无偏移，贴着直线顶部
            val offsetPx = offsetDp.pixels
            val triangleTop = top + offsetPx

            // 创建倒三角形路径
            trianglePath.reset()
            trianglePath.moveTo(x - halfTriangleWidth, triangleTop) // 左上角
            trianglePath.lineTo(x + halfTriangleWidth, triangleTop) // 右上角
            trianglePath.lineTo(x, triangleTop + triangleSizePx) // 底部中心点
            trianglePath.close()

            // 绘制三角形
            canvas.drawPath(trianglePath, trianglePaint)
        }
    }
}
