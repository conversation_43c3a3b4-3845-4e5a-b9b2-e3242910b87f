package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf

@Composable
fun DurationYearlyDemo(modifier: Modifier) {
    // 月份映射工具
    fun getMonthLabel(x: Long): String {
        return when (x.toInt()) {
            648 -> "1月"
            649 -> "2月"
            650 -> "3月"
            651 -> "4月"
            652 -> "5月"
            653 -> "6月"
            654 -> "7月"
            655 -> "8月"
            656 -> "9月"
            657 -> "10月"
            658 -> "11月"
            659 -> "12月"
            else -> "第${x}月"
        }
    }

    // 创建月度持续时间数据
    val chartData = remember {
        val durationSeries = ChartData.Series(
            chartType = ChartType.BAR,
            color = -9643861, // 使用用户提供的颜色值
            axisRange = ChartData.AxisRange(
                minX = 648.0,
                maxX = 659.0,
                minY = 0.0,
                maxY = 2160.0
            ),
            entries = persistentListOf(
                ChartData.Entry(648, 116.32966666666667),
                ChartData.Entry(649, 1421.3011666666669),
                ChartData.Entry(650, 2063.814166666666),
                ChartData.Entry(651, 1200.4415),
                ChartData.Entry(652, 1327.8113333333336),
                ChartData.Entry(653, 214.4993333333333),
                ChartData.Entry(654, 487.3083333333333),
                ChartData.Entry(655, 13.591833333333334),
                ChartData.Entry(656, 22.411166666666666),
                ChartData.Entry(657, 247.33416666666668),
                ChartData.Entry(658, 715.7884999999999),
                ChartData.Entry(659, 417.23333333333335)
            ),
            value = buildAnnotatedString { append("11 小时 27 分") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
            average = null
        )

        // 构建ChartData对象
        ChartData(
            chartContent = ChartContent.DURATION,
            chartGranularity = ChartGranularity.YEARLY,
            series = persistentListOf(durationSeries),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.GROUPED,
            colorIndicator = null,
            selectEntryX = 650L // 设置默认选中3月的柱子（最高值）
        )
    }

    // 记录当前选中的项，初始值设为650（3月）
    var selectedX by remember { mutableStateOf<Long?>(650L) }

    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "年度持续时间统计（按月显示）",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = "总持续时间: 11 小时 27 分",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
        )

        // 显示选中的数据
        selectedX?.let { xValue ->
            val selectedEntry = chartData.series[0].entries.find { it.x == xValue }
            selectedEntry?.let { entry ->
                val monthLabel = getMonthLabel(xValue)
                val yValue = entry.y.toDouble()
                val hours = (yValue / 60.0).toInt()
                val minutes = (yValue % 60.0).toInt()

                Text(
                    text = "$monthLabel: ${hours}小时${minutes}分钟 (${String.format("%.1f", yValue)}分钟)",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                )
            }
        }

        // 数据概览
        Text(
            text = "数据跨度: ${chartData.series[0].entries.size} 个月 (1-12月)",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
        )

        // 渲染图表
        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedX = x },
            onNoEntrySelected = { selectedX = null },
            modifier = Modifier
                .fillMaxWidth()
                .height(350.dp)
        )

        // 数据详情
        Text(
            text = "月度数据详情:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        chartData.series[0].entries.forEach { entry ->
            val monthLabel = getMonthLabel(entry.x)
            val yValue = entry.y.toDouble()
            val hours = (yValue / 60.0).toInt()
            val minutes = (yValue % 60.0).toInt()

            Text(
                text = "• $monthLabel: ${hours}小时${minutes}分钟 (${String.format("%.1f", yValue)}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        // 统计信息
        val avgDuration = chartData.series[0].entries.map { it.y.toDouble() }.average()
        val maxEntry = chartData.series[0].entries.maxByOrNull { it.y.toDouble() }
        val minEntry = chartData.series[0].entries.minByOrNull { it.y.toDouble() }
        val totalDuration = chartData.series[0].entries.sumOf { it.y.toDouble() }

        Text(
            text = "统计信息:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        Text(
            text = "• 总时长: ${String.format("%.1f", totalDuration)}分钟 (${(totalDuration/60).toInt()}小时${(totalDuration%60).toInt()}分钟)",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        Text(
            text = "• 月平均时长: ${String.format("%.1f", avgDuration)}分钟",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        maxEntry?.let { entry ->
            Text(
                text = "• 最长月份: ${getMonthLabel(entry.x)} (${String.format("%.1f", entry.y.toDouble())}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        minEntry?.let { entry ->
            Text(
                text = "• 最短月份: ${getMonthLabel(entry.x)} (${String.format("%.1f", entry.y.toDouble())}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        // 数据趋势分析
        Text(
            text = "趋势分析:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        Text(
            text = "• 3月数据达到峰值，春季活动较为活跃",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        Text(
            text = "• 8-9月数据较低，夏末秋初活动相对较少",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        Text(
            text = "• 11月后有所回升，年底活动增加",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )
    }
}
