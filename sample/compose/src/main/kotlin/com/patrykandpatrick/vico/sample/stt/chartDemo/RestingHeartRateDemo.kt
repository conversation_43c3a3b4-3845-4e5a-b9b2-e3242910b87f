package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

@Composable
fun RestingHeartRateDemo(modifier: Modifier) {
    // 日期格式化工具
    val dateFormatter = remember { SimpleDateFormat("MM/dd", Locale.getDefault()) }

    // 格式化具体日期
    fun formatDate(timestamp: Long): String {
        // 数据X轴时间戳是使用基于2024年的日期序号
        // 创建一个2024年1月1日的基准日期
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 1, 0, 0, 0)
        calendar.set(Calendar.MILLISECOND, 0)

        // 计算目标日期 = 基准日期 + (timestamp - 20000) 天
        val daysToAdd = timestamp - 20000
        calendar.add(Calendar.DAY_OF_YEAR, daysToAdd.toInt())

        return dateFormatter.format(calendar.time)
    }

    // 状态管理
    var selectedEntryX by remember { mutableStateOf<Long?>(null) }
    var selectedValue by remember { mutableStateOf<String?>(null) }
    var selectedDate by remember { mutableStateOf<String?>(null) }

    // 创建基于用户数据的ChartData
    val chartData = remember {
        ChartData(
            chartContent = ChartContent.RESTING_HEART_RATE,
            chartGranularity = ChartGranularity.SEVEN_DAYS,
            series = persistentListOf(
                ChartData.Series(
                    chartType = ChartType.LINE,
                    color = -52429, // 用户提供的颜色
                    axisRange = ChartData.AxisRange(
                        minX = 20284.0,
                        maxX = 20290.0,
                        minY = 50.0,  // 给Y轴设置合理的范围
                        maxY = 70.0   // 让心率值60在中间位置
                    ),
                    entries = persistentListOf(
                        ChartData.Entry(x = 20287, y = 60)
                    ),
                    value = buildAnnotatedString { append("60 bpm") },
                    candlestickEntries = persistentListOf(),
                    lineConfig = LineChartConfig(
                        isSmoothCurve = false,
                        showPoints = true,
                        pointSizeDP = 8.0f,
                        isPointFilled = false,
                        showAreaFill = true,
                        areaAlpha = 0.2f,
                        thicknessDP = null
                    ),
                    gradientEntries = persistentListOf(),
                    backgroundRegion = null,
                    groupStackBarEntries = persistentListOf(),
                    groupStackBarStyle = null,
                    average = 60.0f
                )
            ),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.STACKED,
            colorIndicator = null,
            selectEntryX = null,
        )
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // 标题行
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "静态心率",
                    style = MaterialTheme.typography.headlineSmall
                )
                Text(
                    text = if (selectedDate != null && selectedValue != null) {
                        "$selectedDate: $selectedValue"
                    } else {
                        "7天数据 • 平均值: 60 bpm"
                    },
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 当前值显示
            Column(horizontalAlignment = Alignment.End) {
                Text(
                    text = selectedValue ?: "60 bpm",
                    style = MaterialTheme.typography.headlineMedium,
                    color = if (selectedEntryX != null) MaterialTheme.colorScheme.primary 
                           else MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = selectedDate ?: formatDate(20287),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        // 图表
        Chart(
            chartData = chartData,
            onEntrySelected = { entryX ->
                selectedEntryX = entryX
                selectedDate = formatDate(entryX)
                // 根据x坐标查找对应的y值
                val entry = chartData.series.firstOrNull()?.entries?.find { it.x == entryX }
                selectedValue = entry?.let { "${it.y} bpm" }
            },
            onNoEntrySelected = {
                selectedEntryX = null
                selectedDate = null
                selectedValue = null
            },
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 16.dp)
        )

        // 数据说明
        Text(
            text = "这是一个静态心率数据示例，显示在2024年的指定日期记录的心率值。图表支持长按选择数据点来查看详细信息。",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(top = 16.dp)
        )
    }
} 