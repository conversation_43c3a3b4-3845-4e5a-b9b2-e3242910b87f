package com.patrykandpatrick.vico.sample.stt.chart

import com.patrykandpatrick.vico.core.cartesian.data.CartesianLayerRangeProvider
import com.patrykandpatrick.vico.core.common.data.ExtraStore
import com.patrykandpatrick.vico.sample.stt.model.ChartData

internal class RangeProvider(
  var chartData: ChartData,
) : CartesianLayerRangeProvider {
    override fun getMinX(minX: Double, maxX: Double, extraStore: ExtraStore): Double =
        chartData.series.minOfOrNull { it.axisRange.minX } ?: 0.0

    override fun getMaxX(minX: Double, maxX: Double, extraStore: ExtraStore): Double =
        chartData.series.maxOfOrNull { it.axisRange.maxX } ?: getMinX(minX, maxX, extraStore)

    override fun getMinY(minY: Double, maxY: Double, extraStore: ExtraStore): Double =
        chartData.series.minOfOrNull { it.axisRange.minY } ?: 0.0

    override fun getMaxY(minY: Double, maxY: Double, extraStore: ExtraStore): Double =
        chartData.series.maxOfOrNull { it.axisRange.maxY } ?: getMinY(minY, maxY, extraStore)
}
