package com.patrykandpatrick.vico.sample.stt.model

import androidx.annotation.ColorInt
import androidx.compose.ui.text.AnnotatedString
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.ImmutableMap

data class ChartData(
  val chartContent: ChartContent?,
  val chartGranularity: ChartGranularity,
  val series: ImmutableList<Series>,
  val highlightEnabled: Boolean,
  val goal: Number?,
  val highlightDecorationLines: ImmutableMap<Number, Int>,
  val chartBarDisplayMode: ChartBarDisplayMode = ChartBarDisplayMode.STACKED,
  val colorIndicator: ColorIndicator?,
  val selectEntryX: Long? = null,
) {
  data class Series(
    val chartType: ChartType,
    @ColorInt val color: Int,
    val axisRange: AxisRange,
    val entries: ImmutableList<Entry>,
    val value: AnnotatedString,
    val candlestickEntries: ImmutableList<CandlestickEntry>?,
    val lineConfig: LineChartConfig?,
    val gradientEntries:ImmutableList<GradientLineEntry> = persistentListOf(),
    val backgroundRegion: BackgroundRegion?,
    val groupStackBarEntries: ImmutableList<GroupStackBarEntry> = persistentListOf(),
    val groupStackBarStyle: GroupStackBarStyle?,
    val average: Float? = null,
  )

  data class Entry(
    val x: Long,
    val y: Number,
  )

  data class GradientLineEntry(
    val x: Long,
    val y: Number,
    @ColorInt val color: Int,
  )

  data class AxisRange(
    val minX: Double,
    val maxX: Double,
    val minY: Double,
    val maxY: Double,
  )

  data class CandlestickEntry(
    val x: Long,
    val open: Number,
    val close: Number,
    val low: Number,
    val high: Number,
  )

  data class BackgroundRegion(
    val backgroundRegions: ImmutableList<BackgroundRegionEntry>,
    @ColorInt val backgroundColorInt: Int
  )

  data class BackgroundRegionEntry(
    val x: Long,
    val startY: Float,
    val endY: Float
  )

  data class ColorIndicator(
    val thresholds: ImmutableList<Int>,
    @ColorInt val colors: ImmutableList<Int>,
    val labelTexts: ImmutableList<String>,
    val barThicknessDp: Float,
    val barShiftXDp: Float,
    val useThresholdsAsAxisLines: Boolean,
  )

  data class GroupStackBarEntry(
    val x: Long,
    val y: Number,
    @ColorInt val color: Int,
  )

  data class GroupStackBarStyle(
    val cornerRadiusTopLeftDp: Float?,
    val cornerRadiusTopRightDp: Float?,
    val cornerRadiusBottomLeftDp: Float?,
    val cornerRadiusBottomRightDp: Float?,
    val thicknessDp: Float?,
    @ColorInt val strokeColor: Int?,
    val strokeWidthDp: Float?,
    val strokeDashLengthDp: Float?,
    val strokeGapLengthDp: Float?
  )
}

