package com.patrykandpatrick.vico.sample.stt.chart

import android.annotation.SuppressLint
import android.graphics.Paint
import com.patrykandpatrick.vico.core.common.DrawingContext
import com.patrykandpatrick.vico.core.common.Fill
import com.patrykandpatrick.vico.core.common.MeasuringContext
import com.patrykandpatrick.vico.core.common.component.LineComponent
import com.patrykandpatrick.vico.core.common.half
import com.patrykandpatrick.vico.core.common.shape.Shape

@SuppressLint("RestrictedApi")
class RoundedCornerStackedLineComponent(
    fill: Fill,
    private val prevFill: Fill,
    thicknessDp: Float = 1f,
    shape: Shape,
    private val roundedCornerOffsetDp: Float,
    private val horizontalAxisHeightDp: Float,
) : LineComponent(fill, thicknessDp, shape) {
    val MeasuringContext.thickness: Float
        get() = thicknessDp.pixels

    val MeasuringContext.roundedCornerOffset: Float
        get() = roundedCornerOffsetDp.pixels

    val MeasuringContext.horizontalAxisHeight: Float
        get() = horizontalAxisHeightDp.pixels

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply { color = fill.color }

    override fun drawVertical(
        context: DrawingContext,
        x: Float,
        top: Float,
        bottom: Float,
        thicknessFactor: Float
    ) = with(context) {
        val halfThickness = (thicknessFactor * thickness).half
        val left = x - halfThickness
        val right = x + halfThickness
        val horizontalAxisTop = canvasBounds.bottom - horizontalAxisHeight
        if (bottom != horizontalAxisTop) {
            val adjustedBottom = (bottom + roundedCornerOffset).coerceAtMost(horizontalAxisTop)
            draw(this, left, top, right, adjustedBottom, fill.color)
            if (bottom != adjustedBottom) {
                // avoid subpixel rendering by adding 1 pixel
                val maskBottom = (adjustedBottom + 1).coerceAtMost(horizontalAxisTop)
                draw(this, left, bottom, right, maskBottom, prevFill.color)
            }
        } else {
            draw(this, left, top, right, bottom, fill.color)
        }
    }

    private fun draw(
        context: DrawingContext,
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        color: Int,
    ) {
        with(context) {
            var adjustedLeft = left + margins.getLeft(context)
            var adjustedTop = top + margins.topDp.pixels
            var adjustedRight = right - margins.getRight(context)
            var adjustedBottom = bottom - margins.bottomDp.pixels
            if (adjustedLeft >= adjustedRight || adjustedTop >= adjustedBottom) return
            val strokeThickness = strokeThicknessDp.pixels
            if (strokeThickness != 0f) {
                adjustedLeft += strokeThickness.half
                adjustedTop += strokeThickness.half
                adjustedRight -= strokeThickness.half
                adjustedBottom -= strokeThickness.half
                if (adjustedLeft > adjustedRight || adjustedTop > adjustedBottom) return
            }
            path.rewind()
            applyShader(this, left, top, right, bottom)
            shape.outline(this, path, adjustedLeft, adjustedTop, adjustedRight, adjustedBottom)
            paint.color = color
            canvas.drawPath(path, paint)
        }
    }
}
