package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.compose.cartesian.CartesianChartHost
import com.patrykandpatrick.vico.compose.cartesian.rememberCartesianChart
import com.patrykandpatrick.vico.compose.cartesian.rememberVicoZoomState
import com.patrykandpatrick.vico.core.cartesian.CartesianMeasuringContext
import com.patrykandpatrick.vico.core.cartesian.Zoom
import com.patrykandpatrick.vico.core.cartesian.axis.Axis
import com.patrykandpatrick.vico.core.cartesian.data.CartesianChartModelProducer
import com.patrykandpatrick.vico.core.cartesian.data.CartesianValueFormatter
import com.patrykandpatrick.vico.core.cartesian.marker.CartesianMarker
import com.patrykandpatrick.vico.core.cartesian.marker.CartesianMarkerVisibilityListener
import com.patrykandpatrick.vico.sample.compose.R
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.HighlightMarker
import com.patrykandpatrick.vico.sample.stt.chart.RangeProvider
import com.patrykandpatrick.vico.sample.stt.chart.axis.rememberXAxis
import com.patrykandpatrick.vico.sample.stt.chart.axis.rememberYAxis
import com.patrykandpatrick.vico.sample.stt.chart.prepareData
import com.patrykandpatrick.vico.sample.stt.chart.rememberChartLayers
import com.patrykandpatrick.vico.sample.stt.chart.rememberLineComponentWithTriangle
import com.patrykandpatrick.vico.sample.stt.composeui.modifer.onLongPress
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import com.stt.android.chart.impl.chart.axis.calculateXAxisStep
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.coroutines.delay
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Locale
import kotlin.math.roundToLong
import kotlin.random.Random

// 自定义的valueFormatter，根据月份跨度动态隐藏第一个月标签
private class CustomMonthValueFormatter(
    private val context: android.content.Context,
    private val chartData: ChartData
) : CartesianValueFormatter {
    private var previousMonth: Int = -1
    private var firstMonthValue: Int? = null
    private var shouldHideFirstMonth: Boolean = false
    
    init {
        // 计算月份跨度，判断是否需要隐藏第一个月
        calculateMonthSpan()
    }
    
    private fun calculateMonthSpan() {
        val entries = chartData.series.firstOrNull()?.entries
        if (entries.isNullOrEmpty()) return
        
        // 按时间顺序获取月份，而不是按数值排序
        val monthsWithTime = entries.map { entry ->
            val epochDay = entry.x
            val date = LocalDate.ofEpochDay(epochDay)
            date.monthValue to epochDay
        }.sortedBy { it.second } // 按时间排序
        
        val months = monthsWithTime.map { it.first }.distinct()
        
        if (months.isNotEmpty()) {
            // 第一个月应该是时间上最早的月份，不是数值最小的月份
            firstMonthValue = monthsWithTime.first().first
            
            // 计算月份跨度
            val monthSpan = if (months.size > 1) {
                val firstMonth = monthsWithTime.first().first
                val lastMonth = monthsWithTime.last().first
                
                // 处理跨年情况（如12月到次年6月）
                if (lastMonth < firstMonth) {
                    // 跨年：12月到次年的月份
                    (12 - firstMonth + 1) + lastMonth
                } else {
                    // 同年：正常计算
                    lastMonth - firstMonth + 1
                }
            } else {
                1
            }
            
            // 如果月份跨度大于6个月，则隐藏第一个月标签
            shouldHideFirstMonth = monthSpan > 6
        }
    }

    override fun format(
        context: CartesianMeasuringContext,
        value: Double,
        verticalAxisPosition: Axis.Position.Vertical?,
    ): CharSequence {
        val epochDay = value.roundToLong()
        val month = LocalDate.ofEpochDay(epochDay).monthValue
        
        // 如果需要隐藏第一个月且当前是第一个月，返回零宽度空格
        if (shouldHideFirstMonth && month == firstMonthValue) {
            return "\u200B" // 零宽度空格，不可见但不是空字符串
        }
        
        return if (month != previousMonth) {
            previousMonth = month
            this.context.resources.getStringArray(R.array.abbreviated_months)[month - 1]
        } else {
            " "
        }
    }
}

// 数据生成函数
private fun generateStepsData(startMonth: YearMonth): ChartData {
    val random = Random(startMonth.hashCode()) // 使用月份作为种子，确保数据一致性
    
    // 生成6个月的数据
    val entries = mutableListOf<ChartData.Entry>()
    var currentEpochDay = startMonth.atDay(1).toEpochDay()
    
    repeat(6) { monthIndex ->
        val currentMonth = startMonth.plusMonths(monthIndex.toLong())
        val daysInMonth = currentMonth.lengthOfMonth()
        
        // 每个月生成3-5个数据点
        val dataPointsCount = random.nextInt(3, 6)
        repeat(dataPointsCount) { pointIndex ->
            val dayOfMonth = (pointIndex + 1) * (daysInMonth / (dataPointsCount + 1))
            val epochDay = currentMonth.atDay(dayOfMonth).toEpochDay()
            val steps = random.nextInt(500, 12000).toDouble()
            
            entries.add(ChartData.Entry(x = epochDay, y = steps))
        }
    }
    
    // 计算统计数据
    val averageSteps = entries.map { it.y.toDouble() }.average().toInt()
    val minX = entries.minOf { it.x }.toDouble()
    val maxX = entries.maxOf { it.x }.toDouble()
    
    return ChartData(
        chartContent = ChartContent.STEPS,
        chartGranularity = ChartGranularity.SIX_MONTHS,
        series = persistentListOf(
            ChartData.Series(
                chartType = ChartType.BAR,
                color = -10045697, // 蓝绿色
                axisRange = ChartData.AxisRange(
                    minX = minX - 1,
                    maxX = maxX + 1,
                    minY = 0.0,
                    maxY = 12000.0
                ),
                entries = persistentListOf(*entries.toTypedArray()),
                value = buildAnnotatedString { append("平均: ${averageSteps}步/天") },
                candlestickEntries = persistentListOf(),
                lineConfig = LineChartConfig(
                    isSmoothCurve = true,
                    showPoints = false,
                    pointSizeDP = null,
                    isPointFilled = true,
                    showAreaFill = true,
                    areaAlpha = null,
                    thicknessDP = null
                ),
                backgroundRegion = null,
                groupStackBarEntries = persistentListOf(),
                groupStackBarStyle = null,
                average = null
            )
        ),
        highlightEnabled = true,
        goal = 10000,
        highlightDecorationLines = persistentMapOf(),
        chartBarDisplayMode = ChartBarDisplayMode.STACKED,
        colorIndicator = null,
        selectEntryX = null
    )
}

@Composable
fun StepsMonthlyDemo(modifier: Modifier) {
    val context = LocalContext.current
    
    // 日期格式化工具
    val dateFormatter = remember { SimpleDateFormat("MM/dd", Locale.getDefault()) }
    val monthFormatter = remember { DateTimeFormatter.ofPattern("yyyy年MM月") }
    
    // 格式化具体日期
    fun formatDate(epochDay: Long): String {
        val date = LocalDate.ofEpochDay(epochDay)
        return date.format(DateTimeFormatter.ofPattern("MM/dd"))
    }
    
    // 计算当前月份和页面数量
    val currentMonth = remember { YearMonth.now() }
    val totalPages = 24 // 可以查看过去2年的数据
    
    // 创建PagerState
    val pagerState = rememberPagerState(
        initialPage = 0,
        pageCount = { totalPages }
    )
    
    // 根据当前页面计算对应的月份
    val currentPageMonth by remember {
        derivedStateOf {
            currentMonth.minusMonths(pagerState.currentPage.toLong())
        }
    }
    
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // 标题和时间范围
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "步数月度统计",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                val endMonth = currentPageMonth.plusMonths(5)
                Text(
                    text = "${currentPageMonth.format(monthFormatter)} - ${endMonth.format(monthFormatter)}",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onPrimaryContainer,
                    modifier = Modifier.padding(top = 4.dp)
                )
                
                Text(
                    text = "← 向左滑动查看更早期数据 →",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.7f),
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }
        
        // ViewPager
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            val pageMonth = currentMonth.minusMonths(page.toLong())
            StepsMonthlyPage(
                startMonth = pageMonth,
                formatDate = ::formatDate,
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

@Composable
private fun StepsMonthlyPage(
    startMonth: YearMonth,
    formatDate: (Long) -> String,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    
    // 生成当前页面的数据
    val chartData = remember(startMonth) {
        generateStepsData(startMonth)
    }
    
    var selectedEntryIndex by remember { mutableStateOf<Int?>(null) }
    
    // 计算统计数据
    val maxEntry = remember(chartData) { 
        chartData.series.firstOrNull()?.entries?.maxByOrNull { it.y.toDouble() }
    }
    val minEntry = remember(chartData) { 
        chartData.series.firstOrNull()?.entries?.minByOrNull { it.y.toDouble() }
    }
    val goalAchievedCount = remember(chartData) {
        chartData.series.firstOrNull()?.entries?.count { it.y.toDouble() >= 10000 } ?: 0
    }
    val totalEntries = chartData.series.firstOrNull()?.entries?.size ?: 0
    val averageSteps = remember(chartData) {
        chartData.series.firstOrNull()?.entries?.map { it.y.toDouble() }?.average()?.toInt() ?: 0
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 统计信息卡片
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                // 第一行统计
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatisticItem(
                        label = "最高记录",
                        value = "${maxEntry?.y?.toDouble()?.toInt() ?: 0}步",
                        subtitle = maxEntry?.let { formatDate(it.x) } ?: "",
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    StatisticItem(
                        label = "最低记录", 
                        value = "${minEntry?.y?.toDouble()?.toInt() ?: 0}步",
                        subtitle = minEntry?.let { formatDate(it.x) } ?: "",
                        color = MaterialTheme.colorScheme.error
                    )
                    
                    StatisticItem(
                        label = "平均步数",
                        value = "${averageSteps}步/天",
                        subtitle = "",
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                // 第二行统计
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    StatisticItem(
                        label = "目标达成",
                        value = "$goalAchievedCount/$totalEntries 天",
                        subtitle = "${(goalAchievedCount.toFloat() / totalEntries * 100).toInt()}%",
                        color = if (goalAchievedCount > 0) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.error
                        }
                    )
                    
                    StatisticItem(
                        label = "数据点数量",
                        value = "${totalEntries}个记录",
                        subtitle = "6个月期间",
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    StatisticItem(
                        label = "时间跨度",
                        value = "6个月",
                        subtitle = "月度统计",
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
            }
        }

        // 图表区域
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(300.dp)
        ) {
            CustomChart(
                chartData = chartData,
                onEntrySelected = { x -> 
                    selectedEntryIndex = chartData.series.firstOrNull()?.entries?.indexOfFirst { it.x == x }
                },
                onNoEntrySelected = { selectedEntryIndex = null },
                modifier = Modifier.fillMaxSize()
            )
        }

        // 选中条目的详细信息
        selectedEntryIndex?.let { index ->
            val selectedEntry = chartData.series.firstOrNull()?.entries?.getOrNull(index)
            selectedEntry?.let { entry ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.secondaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    ) {
                        Text(
                            text = "选中数据详情",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onSecondaryContainer,
                            modifier = Modifier.padding(bottom = 12.dp)
                        )
                        
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            DetailItem(
                                label = "日期",
                                value = formatDate(entry.x)
                            )
                            
                            DetailItem(
                                label = "步数",
                                value = "${entry.y.toDouble().toInt()}步"
                            )
                            
                            val percentage = (entry.y.toDouble() / 10000.0 * 100).toInt()
                            DetailItem(
                                label = "目标完成度",
                                value = "${percentage}%"
                            )
                        }
                        
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 12.dp),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            val difference = entry.y.toDouble() - averageSteps
                            val diffText = if (difference > 0) {
                                "+${difference.toInt()}步"
                            } else {
                                "${difference.toInt()}步"
                            }
                            DetailItem(
                                label = "与平均值比较",
                                value = diffText
                            )
                            
                            val rating = when {
                                entry.y.toDouble() >= 8000 -> "优秀"
                                entry.y.toDouble() >= 5000 -> "良好" 
                                entry.y.toDouble() >= 3000 -> "一般"
                                else -> "需改进"
                            }
                            DetailItem(
                                label = "表现评级",
                                value = rating
                            )
                            
                            DetailItem(
                                label = "数据序号",
                                value = "${index + 1}/${totalEntries}"
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun StatisticItem(
    label: String,
    value: String,
    subtitle: String,
    color: androidx.compose.ui.graphics.Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Bold,
            color = color
        )
        if (subtitle.isNotEmpty()) {
            Text(
                text = subtitle,
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun DetailItem(
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.labelSmall,
            color = MaterialTheme.colorScheme.onSecondaryContainer.copy(alpha = 0.7f)
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSecondaryContainer
        )
    }
}

@Composable
private fun CustomChart(
    chartData: ChartData,
    onEntrySelected: (Long) -> Unit,
    onNoEntrySelected: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current

    val modelProducer = remember { CartesianChartModelProducer() }
    val rangeProvider = remember { RangeProvider(chartData) }
    LaunchedEffect(chartData) {
        modelProducer.prepareData(chartData, rangeProvider)
    }

    val markerLine = rememberLineComponentWithTriangle()

    val highlightMarker = remember(chartData.highlightEnabled) {
        HighlightMarker(
            markerLine = markerLine,
            drawOverLayers = false,
        )
    }

    val layers = rememberChartLayers(chartData, rangeProvider, null)

    var selectedEntryX by remember { mutableStateOf<Long?>(null) }
    var chartLongPressed by remember { mutableStateOf(false) }

    // 创建自定义的X轴，使用自定义的valueFormatter
    val customXAxis = rememberXAxis(context, chartData).copy(
        valueFormatter = CustomMonthValueFormatter(context, chartData)
    )

    CartesianChartHost(
        chart = rememberCartesianChart(
            layers = layers.toTypedArray(),
            endAxis = rememberYAxis(chartData),
            bottomAxis = customXAxis,
            marker = remember(chartData.highlightEnabled) {
                if (chartData.highlightEnabled) highlightMarker else null
            },
            markerVisibilityListener = object : CartesianMarkerVisibilityListener {
                override fun onShown(marker: CartesianMarker, targets: List<CartesianMarker.Target>) {
                    onUpdated(marker, targets)
                }

                override fun onUpdated(marker: CartesianMarker, targets: List<CartesianMarker.Target>) {
                    targets.firstOrNull()
                        ?.x
                        ?.roundToLong()
                        ?.let { entryX ->
                            selectedEntryX = entryX
                            if (chartLongPressed) {
                                onEntrySelected(entryX)
                            }
                        }
                        ?: run(onNoEntrySelected)
                }

                override fun onHidden(marker: CartesianMarker) {
                    onNoEntrySelected()
                    selectedEntryX = null
                }
            },
            getXStep = calculateXAxisStep(chartData.chartGranularity),
        ),
        modelProducer = modelProducer,
        modifier = modifier.onLongPress(
            key = Unit,
            onLongPressed = { longPressed ->
                highlightMarker.drawOverLayers = longPressed
                chartLongPressed = longPressed
                if (longPressed) {
                    selectedEntryX?.let(onEntrySelected)
                } else {
                    selectedEntryX = null
                    onNoEntrySelected()
                }
            },
        ),
        zoomState = rememberVicoZoomState(
            zoomEnabled = false,
            initialZoom = Zoom.Content,
        ),
    )
} 