package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.time.LocalDate

@Composable
fun DurationMonthlyDemo(modifier: Modifier) {
    // 月份映射工具
    fun getDateLabel(x: Long): String {
      val targetDate = LocalDate.ofEpochDay(x)
      return "${targetDate.monthValue}/${targetDate.dayOfMonth}"
    }

    // 创建月度持续时间数据
    val chartData = remember {
        val durationSeries = ChartData.Series(
            chartType = ChartType.BAR,
            color = -9643861, // 使用提供的颜色值
            axisRange = ChartData.AxisRange(
                minX = 20240.0,
                maxX = 20269.0,
                minY = 0.0,
                maxY = 180.0
            ),
            entries = persistentListOf(
                ChartData.Entry(20242, 88.53866666666667),
                ChartData.Entry(20244, 45.68566666666666),
                ChartData.Entry(20246, 91.09833333333333),
                ChartData.Entry(20247, 32.640166666666666),
                ChartData.Entry(20249, 100.45583333333335),
                ChartData.Entry(20250, 3.64),
                ChartData.Entry(20251, 135.914),
                ChartData.Entry(20252, 92.83933333333333),
                ChartData.Entry(20254, 93.61583333333333)
            ),
            value = buildAnnotatedString { append("11 小时 24 分") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
        )

        // 构建ChartData对象
        ChartData(
            chartContent = ChartContent.DURATION,
            chartGranularity = ChartGranularity.MONTHLY,
            series = persistentListOf(durationSeries),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.GROUPED,
            colorIndicator = null,
            selectEntryX = 20249L // 🎯 设置默认选中9月份的柱子
        )
    }

    // 记录当前选中的项，🎯 初始值设为20249（9月份）
    var selectedX by remember { mutableStateOf<Long?>(20249L) }

    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "月度持续时间统计",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = "总持续时间: 11 小时 24 分",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
        )

        // 显示选中的数据
        selectedX?.let { xValue ->
            val selectedEntry = chartData.series[0].entries.find { it.x == xValue }
            selectedEntry?.let { entry ->
                val monthLabel = getDateLabel(xValue)
                val yValue = entry.y.toDouble()
                val hours = (yValue / 60.0).toInt()
                val minutes = (yValue % 60.0).toInt()

                Text(
                    text = "$monthLabel: ${hours}小时${minutes}分钟 (${String.format("%.1f", yValue)}分钟)",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                )
            }
        }

        // 数据概览
        Text(
            text = "数据点: ${chartData.series[0].entries.size} 个月",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
        )

        // 渲染图表
        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedX = x },
            onNoEntrySelected = { selectedX = null },
            modifier = Modifier
                .fillMaxWidth()
                .height(350.dp)
        )

        // 数据详情
        Text(
            text = "数据详情:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        chartData.series[0].entries.forEach { entry ->
            val monthLabel = getDateLabel(entry.x)
            val yValue = entry.y.toDouble()
            val hours = (yValue / 60.0).toInt()
            val minutes = (yValue % 60.0).toInt()

            Text(
                text = "• $monthLabel: ${hours}小时${minutes}分钟",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        // 统计信息
        val avgDuration = chartData.series[0].entries.map { it.y.toDouble() }.average()
        val maxEntry = chartData.series[0].entries.maxByOrNull { it.y.toDouble() }
        val minEntry = chartData.series[0].entries.minByOrNull { it.y.toDouble() }

        Text(
            text = "统计信息:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        Text(
            text = "• 平均时长: ${String.format("%.1f", avgDuration)}分钟",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        maxEntry?.let { entry ->
            Text(
                text = "• 日期: ${getDateLabel(entry.x)} (${String.format("%.1f", entry.y.toDouble())}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        minEntry?.let { entry ->
            Text(
                text = "• 日期: ${getDateLabel(entry.x)} (${String.format("%.1f", entry.y.toDouble())}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }
    }
}
