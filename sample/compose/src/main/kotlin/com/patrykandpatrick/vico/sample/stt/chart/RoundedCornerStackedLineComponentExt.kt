package com.patrykandpatrick.vico.sample.stt.chart

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import com.patrykandpatrick.vico.core.common.Fill
import com.patrykandpatrick.vico.core.common.shape.Shape
import com.patrykandpatrick.vico.sample.stt.chart.RoundedCornerStackedLineComponent

@Composable
fun rememberRoundedCornerStackedLineComponent(
    fill: Fill,
    prevFill: Fill,
    thicknessDp: Float,
    shape: Shape,
    roundedCornerOffsetDp: Float,
    horizontalAxisHeightDp: Float,
) = remember(fill, prevFill, thicknessDp, shape, roundedCornerOffsetDp, horizontalAxisHeightDp) {
    RoundedCornerStackedLineComponent(
        fill,
        prevFill,
        thicknessDp,
        shape,
        roundedCornerOffsetDp,
        horizontalAxisHeightDp,
    )
}
