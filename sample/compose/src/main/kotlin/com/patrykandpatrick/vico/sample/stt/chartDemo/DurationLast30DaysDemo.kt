package com.patrykandpatrick.vico.sample.stt.chartDemo

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.dp
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartContent
import com.patrykandpatrick.vico.sample.stt.apiModel.ChartGranularity
import com.patrykandpatrick.vico.sample.stt.chart.Chart
import com.patrykandpatrick.vico.sample.stt.model.ChartBarDisplayMode
import com.patrykandpatrick.vico.sample.stt.model.ChartData
import com.patrykandpatrick.vico.sample.stt.model.ChartType
import com.patrykandpatrick.vico.sample.stt.model.LineChartConfig
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import java.time.LocalDate
import java.time.temporal.ChronoUnit

@Composable
fun DurationLast30DaysDemo(modifier: Modifier) {
    val context = LocalContext.current
    
    // 日期映射工具 - 将epochDay转换为日期标签
    fun getDateLabel(x: Long): String {
        val targetDate = LocalDate.ofEpochDay(x)
        return "${targetDate.monthValue}/${targetDate.dayOfMonth}"
    }

    // 创建基于提供数据的最近30天持续时间数据
    val chartData = remember {
        val durationSeries = ChartData.Series(
            chartType = ChartType.BAR,
            color = -9643861, // 使用提供的颜色值
            axisRange = ChartData.AxisRange(
                minX = 20164.0,
                maxX = 20193.0,
                minY = 0.0,
                maxY = 360.0
            ),
            entries = persistentListOf(
                ChartData.Entry(20164, 0.4075),
                ChartData.Entry(20165, 1.8033333333333335),
                ChartData.Entry(20167, 53.211999999999996),
                ChartData.Entry(20168, 168.85),
                ChartData.Entry(20169, 136.98733333333334),
                ChartData.Entry(20170, 315.7355),
                ChartData.Entry(20173, 12.812333333333333),
                ChartData.Entry(20174, 51.12416666666666),
                ChartData.Entry(20177, 322.03333333333336),
                ChartData.Entry(20181, 0.5896666666666667),
                ChartData.Entry(20182, 188.36566666666667),
                ChartData.Entry(20183, 163.54783333333336),
                ChartData.Entry(20184, 174.16866666666667),
                ChartData.Entry(20185, 67.63366666666667),
                ChartData.Entry(20186, 54.00666666666667),
                ChartData.Entry(20187, 115.63016666666665),
                ChartData.Entry(20188, 46.09866666666667),
                ChartData.Entry(20189, 141.38083333333333),
                ChartData.Entry(20190, 181.56416666666667),
                ChartData.Entry(20193, 34.1045)
            ),
            value = buildAnnotatedString { append("37 小时 10 分") },
            candlestickEntries = persistentListOf(),
            lineConfig = LineChartConfig(
                isSmoothCurve = true,
                showPoints = false,
                pointSizeDP = null,
                isPointFilled = true,
                showAreaFill = true,
                areaAlpha = null,
                thicknessDP = null
            ),
            gradientEntries = persistentListOf(),
            backgroundRegion = null,
            groupStackBarEntries = persistentListOf(),
            groupStackBarStyle = null,
        )

        // 构建ChartData对象
        ChartData(
            chartContent = ChartContent.DURATION,
            chartGranularity = ChartGranularity.THIRTY_DAYS,
            series = persistentListOf(durationSeries),
            highlightEnabled = true,
            goal = null,
            highlightDecorationLines = persistentMapOf(),
            chartBarDisplayMode = ChartBarDisplayMode.GROUPED,
            colorIndicator = null,
            selectEntryX = null // 初始不选中任何项
        )
    }

    // 记录当前选中的项
    var selectedX by remember { mutableStateOf<Long?>(null) }

    Column(modifier = modifier.fillMaxWidth()) {
        Text(
            text = "最近30天持续时间统计 ${getDateLabel(20164)}-${getDateLabel(20193)}",
            style = MaterialTheme.typography.titleMedium,
            modifier = Modifier.padding(16.dp)
        )

        Text(
            text = "总持续时间: 37 小时 10 分",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
        )

        // 显示选中的数据
        selectedX?.let { xValue ->
            val selectedEntry = chartData.series[0].entries.find { it.x == xValue }
            selectedEntry?.let { entry ->
                val dateLabel = getDateLabel(xValue)
                val yValue = entry.y.toDouble()
                val hours = (yValue / 60.0).toInt()
                val minutes = (yValue % 60.0).toInt()

                Text(
                    text = "$dateLabel: ${hours}小时${minutes}分钟 (${String.format("%.1f", yValue)}分钟)",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.secondary,
                    modifier = Modifier.padding(horizontal = 16.dp, vertical = 4.dp)
                )
            }
        }

        // 数据概览
        Text(
            text = "数据点: ${chartData.series[0].entries.size} 天 | 数据范围: ${20193 - 20164 + 1}天",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 2.dp)
        )

        // 渲染图表
        Chart(
            chartData = chartData,
            onEntrySelected = { x -> selectedX = x },
            onNoEntrySelected = { selectedX = null },
            modifier = Modifier
                .fillMaxWidth()
                .height(350.dp)
                .padding(start = 16.dp, top = 16.dp, bottom = 16.dp, end = 32.dp)
        )

        // 数据详情
        Text(
            text = "数据详情:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        chartData.series[0].entries.take(10).forEach { entry -> // 只显示前10个数据点
            val dateLabel = getDateLabel(entry.x)
            val yValue = entry.y.toDouble()
            val hours = (yValue / 60.0).toInt()
            val minutes = (yValue % 60.0).toInt()

            Text(
                text = "• $dateLabel: ${hours}小时${minutes}分钟",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }
        
        if (chartData.series[0].entries.size > 10) {
            Text(
                text = "... 还有 ${chartData.series[0].entries.size - 10} 个数据点",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        // 统计信息
        val avgDuration = chartData.series[0].entries.map { it.y.toDouble() }.average()
        val maxEntry = chartData.series[0].entries.maxByOrNull { it.y.toDouble() }
        val minEntry = chartData.series[0].entries.minByOrNull { it.y.toDouble() }

        Text(
            text = "统计信息:",
            style = MaterialTheme.typography.labelMedium,
            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
        )

        Text(
            text = "• 平均时长: ${String.format("%.1f", avgDuration)}分钟",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
        )

        maxEntry?.let { entry ->
            Text(
                text = "• 最长时间: ${getDateLabel(entry.x)} (${String.format("%.1f", entry.y.toDouble())}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }

        minEntry?.let { entry ->
            Text(
                text = "• 最短时间: ${getDateLabel(entry.x)} (${String.format("%.1f", entry.y.toDouble())}分钟)",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 2.dp)
            )
        }
    }
} 