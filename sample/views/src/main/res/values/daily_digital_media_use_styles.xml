<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<resources>
    <color name="daily_digital_media_use_column_1_color">#6438a7</color>
    <color name="daily_digital_media_use_column_2_color">#3490de</color>
    <color name="daily_digital_media_use_column_3_color">#73e8dc</color>

    <style name="DailyDigitalMediaUseColumn1Style">
        <item name="android:color">@color/daily_digital_media_use_column_1_color</item>
    </style>

    <style name="DailyDigitalMediaUseColumn2Style">
        <item name="android:color">@color/daily_digital_media_use_column_2_color</item>
    </style>

    <style name="DailyDigitalMediaUseColumn3Style">
        <item name="android:color">@color/daily_digital_media_use_column_3_color</item>
    </style>

    <style name="DailyDigitalMediaUseColumnLayerStyle">
        <item name="column1Style">@style/DailyDigitalMediaUseColumn1Style</item>
        <item name="column2Style">@style/DailyDigitalMediaUseColumn2Style</item>
        <item name="column3Style">@style/DailyDigitalMediaUseColumn3Style</item>
        <item name="columnCollectionSpacing">32dp</item>
        <item name="mergeMode">stacked</item>
    </style>

    <style name="DailyDigitalMediaUseBottomAxisStyle">
        <item name="horizontalAxisItemPlacer">segmented</item>
    </style>

    <style name="DailyDigitalMediaUseChartStyle">
        <item name="bottomAxisStyle">@style/DailyDigitalMediaUseBottomAxisStyle</item>
        <item name="columnLayerStyle">@style/DailyDigitalMediaUseColumnLayerStyle</item>
        <item name="layers">column</item>
        <item name="scalableEndLayerPadding">16dp</item>
        <item name="scalableStartLayerPadding">16dp</item>
        <item name="showBottomAxis">true</item>
        <item name="showStartAxis">true</item>
    </style>
</resources>
