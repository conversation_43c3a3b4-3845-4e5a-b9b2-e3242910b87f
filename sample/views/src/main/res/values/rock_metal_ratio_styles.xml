<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<resources>
    <style name="RockMetalRatioColumn1Style">
        <item name="android:color">#ff5500</item>
    </style>

    <style name="RockMetalRatioColumnLayerStyle">
        <item name="column1Style">@style/RockMetalRatioColumn1Style</item>
    </style>

    <style name="RockMetalRatioBottomAxisStyle">
        <item name="horizontalAxisItemPlacer">segmented</item>
    </style>

    <style name="RockMetalRatioChartStyle">
        <item name="bottomAxisStyle">@style/RockMetalRatioBottomAxisStyle</item>
        <item name="columnLayerStyle">@style/RockMetalRatioColumnLayerStyle</item>
        <item name="layers">column</item>
        <item name="scalableEndLayerPadding">8dp</item>
        <item name="scalableStartLayerPadding">8dp</item>
        <item name="showBottomAxis">true</item>
        <item name="showStartAxis">true</item>
    </style>
</resources>
