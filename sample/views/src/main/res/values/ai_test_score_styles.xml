<?xml version="1.0" encoding="utf-8"?><!--
  ~ Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<resources>
    <color name="ai_test_score_line_1_color">#916cda</color>
    <color name="ai_test_score_line_2_color">#d877d8</color>
    <color name="ai_test_score_line_3_color">#f094bb</color>

    <style name="AITestScorePointShapeStyle">
        <item name="cornerSize">50%</item>
        <item name="cornerTreatment">rounded</item>
    </style>

    <style name="AITestScorePoint1Style">
        <item name="android:color">@color/ai_test_score_line_1_color</item>
        <item name="shapeStyle">@style/AITestScorePointShapeStyle</item>
    </style>

    <style name="AITestScorePoint2Style">
        <item name="android:color">@color/ai_test_score_line_2_color</item>
        <item name="shapeStyle">@style/AITestScorePointShapeStyle</item>
    </style>

    <style name="AITestScorePoint3Style">
        <item name="android:color">@color/ai_test_score_line_3_color</item>
        <item name="shapeStyle">@style/AITestScorePointShapeStyle</item>
    </style>

    <style name="AITestScoreLine1Style">
        <item name="android:color">@color/ai_test_score_line_1_color</item>
        <item name="pointStyle">@style/AITestScorePoint1Style</item>
    </style>

    <style name="AITestScoreLine2Style">
        <item name="android:color">@color/ai_test_score_line_2_color</item>
        <item name="pointStyle">@style/AITestScorePoint2Style</item>
    </style>

    <style name="AITestScoreLine3Style">
        <item name="android:color">@color/ai_test_score_line_3_color</item>
        <item name="pointStyle">@style/AITestScorePoint3Style</item>
    </style>

    <style name="AITestScoreLineLayerStyle">
        <item name="line1Style">@style/AITestScoreLine1Style</item>
        <item name="line2Style">@style/AITestScoreLine2Style</item>
        <item name="line3Style">@style/AITestScoreLine3Style</item>
    </style>

    <style name="AITestScoreChartStyle">
        <item name="layers">line</item>
        <item name="lineLayerStyle">@style/AITestScoreLineLayerStyle</item>
        <item name="showBottomAxis">true</item>
        <item name="showStartAxis">true</item>
    </style>
</resources>
