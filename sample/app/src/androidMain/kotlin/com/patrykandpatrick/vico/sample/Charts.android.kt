/*
 * Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.patrykandpatrick.vico.sample

import androidx.compose.ui.Modifier
import com.patrykandpatrick.vico.sample.compose.JetpackComposeAITestScores
import com.patrykandpatrick.vico.sample.compose.JetpackComposeBasicColumnChart
import com.patrykandpatrick.vico.sample.compose.JetpackComposeBasicComboChart
import com.patrykandpatrick.vico.sample.compose.JetpackComposeBasicLineChart
import com.patrykandpatrick.vico.sample.compose.JetpackComposeCandlestickWithVolumeChart
import com.patrykandpatrick.vico.sample.compose.JetpackComposeCryptoPriceChart
import com.patrykandpatrick.vico.sample.stt.JetpackComposeDailyCandlestickChart
import com.patrykandpatrick.vico.sample.compose.JetpackComposeDailyDigitalMediaUse
import com.patrykandpatrick.vico.sample.compose.JetpackComposeElectricCarSales
import com.patrykandpatrick.vico.sample.compose.JetpackComposeGoldPrices
import com.patrykandpatrick.vico.sample.compose.JetpackComposeRockMetalRatios
import com.patrykandpatrick.vico.sample.compose.JetpackComposeStockPriceChart
import com.patrykandpatrick.vico.sample.compose.JetpackComposeTemperatureAnomalies
import com.patrykandpatrick.vico.sample.stt.chartDemo.ActivityResourcesDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.AscentWeeklyDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.DurationMonthlyDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.DurationLast30DaysDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.DurationMonthlyDemo2
import com.patrykandpatrick.vico.sample.stt.chartDemo.DurationYearlyDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.GroupedBarChartDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.HeartRateCandlestickDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.RestingHeartRateDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.StackedBarChartDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.StepsWeeklyDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.DurationSixMonthsDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.StepsMonthlyDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.VO2MaxDemo
import com.patrykandpatrick.vico.sample.stt.chartDemo.VO2MaxYearlyDemo
import com.patrykandpatrick.vico.sample.stt.chartScreen.ChartPagerScreen
import com.patrykandpatrick.vico.sample.views.ViewAITestScores
import com.patrykandpatrick.vico.sample.views.ViewBasicColumnChart
import com.patrykandpatrick.vico.sample.views.ViewBasicComboChart
import com.patrykandpatrick.vico.sample.views.ViewBasicLineChart
import com.patrykandpatrick.vico.sample.views.ViewDailyDigitalMediaUse
import com.patrykandpatrick.vico.sample.views.ViewElectricCarSales
import com.patrykandpatrick.vico.sample.views.ViewGoldPrices
import com.patrykandpatrick.vico.sample.views.ViewRockMetalRatios
import com.patrykandpatrick.vico.sample.views.ViewTemperatureAnomalies

actual val Charts.overridden: LinkedHashMap<UIFramework, List<Chart>>?
  get() =
    linkedMapOf(
      UIFramework.JetpackCompose to
        default(
          { JetpackComposeBasicColumnChart(it) },
          { JetpackComposeBasicLineChart(it) },
          { JetpackComposeBasicComboChart(it) },
          { JetpackComposeAITestScores(it) },
          { JetpackComposeDailyDigitalMediaUse(it) },
          { JetpackComposeTemperatureAnomalies(it) },
          { JetpackComposeElectricCarSales(it) },
          { JetpackComposeRockMetalRatios(it) },
          { JetpackComposeGoldPrices(it) },
          { JetpackComposeStockPriceChart(it) },
          { JetpackComposeCryptoPriceChart(it) },
          { JetpackComposeCandlestickWithVolumeChart(it) },
          { JetpackComposeDailyCandlestickChart(it) },
        ),
      UIFramework.ComposeMultiplatform to ComposeMultiplatform,
      UIFramework.Views to
        default(
          { ViewBasicColumnChart(it) },
          { ViewBasicLineChart(it) },
          { ViewBasicComboChart(it) },
          { ViewAITestScores(it) },
          { ViewDailyDigitalMediaUse(it) },
          { ViewTemperatureAnomalies(it) },
          { ViewElectricCarSales(it) },
          { ViewRockMetalRatios(it) },
          { ViewGoldPrices(it) },
        ),
//      UIFramework.CandlestickCharts to
//        listOf(
//          Chart(Chart.Details.StockPriceChart) { JetpackComposeStockPriceChart(it) },
//          Chart(Chart.Details.CryptoPriceChart) { JetpackComposeCryptoPriceChart(it) },
//          Chart(Chart.Details.CandlestickWithVolumeChart) { JetpackComposeCandlestickWithVolumeChart(it) },
//          Chart(Chart.Details.DailyCandlestickChart) { JetpackComposeDailyCandlestickChart(it) },
//        ),
      UIFramework.ChartExamples to
        listOf(
          Chart(Chart.Details("图表分页展示", "滑动切换查看多种图表类型，包含柱状图、折线图、蜡烛图等")) { modifier -> ChartPagerScreen(modifier) },
          Chart(Chart.Details("活动状态资源图表", "展示一天中不同时段的活动状态分布，包含活跃、不活跃、有压力、正在恢复等状态")) { modifier -> ActivityResourcesDemo(modifier) },
          Chart(Chart.Details("上升高度周数据", "展示一周内的上升高度数据，总计330米")) { modifier -> AscentWeeklyDemo(modifier) },
          Chart(Chart.Details("月度持续时间统计", "展示月度持续时间数据，总计11小时24分")) { modifier -> DurationMonthlyDemo(modifier) },
          Chart(Chart.Details("月度持续时间统计2", "展示30天跨度的持续时间数据，总计17小时29分")) { modifier -> DurationMonthlyDemo2(modifier) },
          Chart(Chart.Details("最近30天持续时间", "展示最近30天任意日期范围的持续时间数据，总计37小时10分，测试智能标签定位")) { modifier -> DurationLast30DaysDemo(modifier) },
          Chart(Chart.Details("年度持续时间统计", "使用年度粒度展示12个月的持续时间数据，总计11小时27分")) { modifier -> DurationYearlyDemo(modifier) },
          Chart(Chart.Details("VO₂ Max 图表", "展示VO₂Max数据的图表，包含颜色阈值")) { modifier -> VO2MaxDemo(modifier) },
          Chart(Chart.Details("VO₂ Max 年度数据", "展示一年内的VO₂Max数据变化趋势")) { modifier -> VO2MaxYearlyDemo(modifier) },
          Chart(Chart.Details("分组柱状图", "展示步数与卡路里的分组柱状图对比")) { modifier -> GroupedBarChartDemo(modifier) },
          Chart(Chart.Details("堆叠柱状图", "展示不同强度活动时长的堆叠柱状图")) { modifier -> StackedBarChartDemo(modifier) },
          Chart(Chart.Details("心率蜡烛图", "展示心率月度数据的蜡烛图，包含最高、最低、开盘和收盘值")) { modifier -> HeartRateCandlestickDemo(modifier) },
          Chart(Chart.Details("静态心率图表", "展示静态心率数据的折线图，包含数据点高亮和区域填充")) { modifier -> RestingHeartRateDemo(modifier) },
          Chart(Chart.Details("每周步数统计", "展示6个月期间每周平均步数变化，包含目标达成情况分析")) { modifier -> StepsWeeklyDemo(modifier) },
          Chart(Chart.Details("步数月度统计", "长期步数变化趋势分析，展示20个数据点的详细统计")) { modifier -> StepsMonthlyDemo(modifier) },
          Chart(Chart.Details("6个月持续时间统计", "展示6个月粒度的睡眠持续时间数据，包含目标值和详细统计信息")) { modifier -> DurationSixMonthsDemo(modifier) },
        ),
    )
