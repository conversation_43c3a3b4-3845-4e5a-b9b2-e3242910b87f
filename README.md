# Vico

![](https://img.shields.io/maven-central/v/com.patrykandpatrick.vico/core)
![](https://img.shields.io/github/actions/workflow/status/patrykandpatrick/vico/build-project.yml?branch=master)
![](https://kotlin-version.aws.icerock.dev/kotlin-version?group=com.patrykandpatrick.vico&name=core)

![](https://patrykandpatrick.com/vico/images/cover.png)

Vico is a powerful and extensible multiplatform chart library. It offers native support for Jetpack
Compose, Compose Multiplatform, and the Android view system.

Learn more in [the guide](https://patrykandpatrick.com/vico/guide).
