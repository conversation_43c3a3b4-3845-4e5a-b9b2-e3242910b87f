/*
 * Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.patrykandpatrick.vico.multiplatform.cartesian

import com.patrykandpatrick.vico.multiplatform.cartesian.data.CartesianChartModel

/** Defines when an automatic scroll should be performed. */
public fun interface AutoScrollCondition {
  /**
   * Given a chart’s new and old models, defines whether an automatic scroll should be performed.
   */
  public fun shouldScroll(oldModel: CartesianChartModel?, newModel: CartesianChartModel): Boolean

  public companion object {
    /** Prevents any automatic scrolling from occurring. */
    public val Never: AutoScrollCondition = AutoScrollCondition { _, _ -> false }

    /**
     * Triggers an automatic scroll when the size of the model increases (that is, the contents of
     * the chart become wider).
     */
    public val OnModelGrowth: AutoScrollCondition = AutoScrollCondition { oldModel, newModel ->
      oldModel != null &&
        (newModel.models.size > oldModel.models.size || newModel.width > oldModel.width)
    }
  }
}
