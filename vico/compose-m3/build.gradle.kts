/*
 * Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

plugins {
  `dokka-convention`
  `publishing-convention`
  id("com.android.library")
  id("kotlin-android")
  id("org.jetbrains.kotlin.plugin.compose")
}

android {
  configure()
  kotlinOptions { jvmTarget = JavaVersion.VERSION_1_8.toString() }
  namespace = moduleNamespace
}

kotlin { explicitApi() }

composeCompiler { reportsDestination = layout.buildDirectory.dir("reports") }

dependencies {
  api(project(":vico:compose"))
  implementation(platform(libs.composeBom))
  implementation(libs.composeMaterial3)
}
