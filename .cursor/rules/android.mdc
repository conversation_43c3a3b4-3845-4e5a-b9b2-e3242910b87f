---
description: 
globs: 
alwaysApply: true
---
# Vico 图表框架测试项目规则

## 项目概述

本项目是一个图表框架测试项目，主要用于测试和展示基于 Vico 库开发的各种图表组件。

- 项目核心 API 基于 @vico 库完成
- 通过 @sample/compose/src/main/kotlin/com/patrykandpatrick/vico/sample/stt/chart/Chart.kt 进行二次封装
- 定义了相关数据模型 @sample/compose/src/main/kotlin/com/patrykandpatrick/vico/sample/stt/model
- 示例图表展示在 @sample/compose/src/main/kotlin/com/patrykandpatrick/vico/sample/stt/chartDemo 文件夹中

## 项目架构

### 核心组件

1. **Vico 库** - 基础图表库，提供图表渲染的核心功能
2. **Chart.kt** - 对 Vico 库的二次封装，简化图表创建过程
3. **数据模型** - 定义了图表所需的数据结构，位于 model 文件夹
4. **示例图表** - 各类图表的具体实现，位于 chartDemo 文件夹
5. **Charts.android.kt** - 注册和展示图表示例的索引文件

### 目录结构

```
vico/
├── core/                   # Vico 库核心组件
├── compose/                # Compose 相关实现
└── ...

sample/
├── compose/
│   └── src/main/kotlin/com/patrykandpatrick/vico/sample/stt/
│       ├── chart/          # 图表组件封装
│       ├── model/          # 数据模型定义
│       └── chartDemo/      # 图表示例实现
└── app/src/androidMain/kotlin/com/patrykandpatrick/vico/sample/
    └── Charts.android.kt   # 图表示例注册和展示
```

## 开发流程

### 1. 通过数据模型控制图表

项目整体流程是基于 chartData 来控制图表的样式。开发新的图表功能时，应遵循以下步骤：

1. 在 @sample/compose/src/main/kotlin/com/patrykandpatrick/vico/sample/stt/model 中定义或扩展数据模型
2. 使用 Chart.kt 提供的接口实现图表渲染
3. 如果需要支持新的样式，参考下面的"修改框架代码"部分

### 2. 修改框架代码

如遇到当前框架不支持的样式，需要按照以下流程修改框架代码：

1. 查阅 @vico 基本 API 文档，了解相关功能的实现方式
2. 在 @sample/compose/src/main/kotlin/com/patrykandpatrick/vico/sample/stt/chart 中修改或扩展 Chart.kt 以支持新样式
3. 确保修改后的框架代码仍能通过数据模型控制图表样式
4. 添加适当的注释，说明修改的目的和用法

### 3. 创建新的示例图表

添加新的图表示例时，应遵循以下步骤：

1. 在 @sample/compose/src/main/kotlin/com/patrykandpatrick/vico/sample/stt/chartDemo 文件夹中创建新的示例文件
2. 示例文件命名应遵循 `[图表类型]Example.kt` 或 `[图表类型]Demo.kt` 的格式
3. 实现示例图表，确保使用已定义的数据模型和 Chart.kt 组件
4. 在 @sample/app/src/androidMain/kotlin/com/patrykandpatrick/vico/sample/Charts.android.kt 文件中注册示例

### 4. 注册示例图表

所有新创建的示例图表都需要添加到 Charts.android.kt 文件的 `UIFramework.ChartExamples` 列表中：

```kotlin
UIFramework.ChartExamples to
  listOf(
    Chart(Chart.Details("示例名称", "示例描述")) { 示例实现类(it) },
    // 添加新示例
  ),
```

## 开发规范

### 命名规范

1. **示例文件命名**: `[图表类型]Example.kt` 或 `[图表类型]Demo.kt`
2. **组件命名**: 使用描述性名称，反映组件功能
3. **变量命名**: 使用驼峰命名法，名称应反映变量用途

### 代码风格

1. 遵循 Kotlin 官方代码风格指南
2. 使用适当的注释说明代码功能和用途
3. 在适当的地方使用 `remember` 和 `LaunchedEffect` 以优化性能

### 测试规范

1. 新添加的图表示例应在不同设备和屏幕尺寸上进行测试
2. 确保图表在各种数据情况下都能正确显示
3. 测试图表的交互功能，如长按选择数据点

## 提交指南

1. 提交前确保代码能正常编译和运行
2. 提交信息应清晰描述更改内容和目的
3. 对于框架代码的修改，应在提交信息中详细说明修改原因和实现方式