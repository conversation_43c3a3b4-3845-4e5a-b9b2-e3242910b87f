name: Check formatting
on:
  push:
  pull_request:
jobs:
  check-formatting:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          java-version: 21
          distribution: zulu
      - run: |
          curl -sSL -o ktfmt.jar https://github.com/facebook/ktfmt/releases/download/v0.54/ktfmt-0.54-jar-with-dependencies.jar
          java -jar ktfmt.jar --dry-run --google-style --set-exit-if-changed .
