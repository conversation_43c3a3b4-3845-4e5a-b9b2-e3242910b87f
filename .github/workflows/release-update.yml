name: Release update
on:
  workflow_dispatch:
jobs:
  release-update:
    runs-on: ubuntu-latest
    if: github.repository == 'patry<PERSON>dpatrick/vico'
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          path: vico
      - uses: actions/setup-java@v4
        with:
          java-version: 17
          distribution: zulu
      - uses: gradle/gradle-build-action@v3
      - run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Pat<PERSON><PERSON> & Patrick Bot"
          git clone https://${{ secrets.BOT_PAT }}@github.com/patrykandpatrick/vico-api-reference
          cd ${{ github.workspace }}/vico
          VERSION_NAME=$(grep -oP "(?<=const val VICO = \")[^\"]*" buildSrc/src/main/kotlin/Versions.kt)
          echo "VERSION_NAME=$VERSION_NAME" >> $GITHUB_ENV
          echo "TAG_NAME=v$VERSION_NAME" >> $GITHUB_ENV
          IS_PRERELEASE=false
          echo $VERSION_NAME | grep -q "alpha\|beta" && IS_PRERELEASE=true
          echo "IS_PRERELEASE=$IS_PRERELEASE" >> $GITHUB_ENV
          ./gradlew assembleDebug
      - uses: softprops/action-gh-release@v2
        with:
          draft: true
          files: ${{ github.workspace }}/vico/sample/app/build/outputs/apk/debug/*.apk
          prerelease: ${{ env.IS_PRERELEASE }}
          tag_name: ${{ env.TAG_NAME }}
          token: ${{ secrets.BOT_PAT }}
      - run: |
          cd ${{ github.workspace }}/vico
          git remote set-url origin https://patrykandpatrickbot:${{ secrets.BOT_PAT }}@github.com/patrykandpatrick/vico
          ./gradlew dokkaGenerate
          API_REFERENCE_DESTINATION=${{ github.workspace }}/vico-api-reference/$(if $IS_PRERELEASE; then echo $VERSION_NAME; else echo stable; fi)
          rm -fr $API_REFERENCE_DESTINATION/*
          cp -R ${{ github.workspace }}/vico/vico/build/dokka/html/. $API_REFERENCE_DESTINATION
          cd ${{ github.workspace }}/vico-api-reference
          git remote set-url origin https://patrykandpatrickbot:${{ secrets.BOT_PAT }}@github.com/patrykandpatrick/vico-api-reference
          git add -A
          git diff --staged --quiet HEAD || git commit -m "Update API reference"
          git push origin
          cd ${{ github.workspace }}/vico
          git push origin
          ./gradlew publishToMavenCentral --no-configuration-cache
        env:
          ORG_GRADLE_PROJECT_mavenCentralPassword: ${{ secrets.OSSRH_TOKEN_PASSWORD }}
          ORG_GRADLE_PROJECT_mavenCentralUsername: ${{ secrets.OSSRH_TOKEN }}
          ORG_GRADLE_PROJECT_signingInMemoryKey: ${{ secrets.GPG_KEY }}
          ORG_GRADLE_PROJECT_signingInMemoryKeyPassword: ${{ secrets.GPG_KEY_PASSWORD }}
