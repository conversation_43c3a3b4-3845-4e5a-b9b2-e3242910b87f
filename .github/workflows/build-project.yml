name: Build project
on:
  push:
  pull_request:
jobs:
  build-project:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-java@v4
        with:
          java-version: 17
          distribution: zulu
      - uses: gradle/gradle-build-action@v3
      - run: ./gradlew assembleDebug
      - uses: actions/upload-artifact@v4
        with:
          name: sample-debug
          path: sample/app/build/outputs/apk/debug/**.apk
