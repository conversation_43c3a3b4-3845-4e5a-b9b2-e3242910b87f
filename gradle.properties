#
# Copyright 2025 by <PERSON><PERSON><PERSON> and <PERSON>.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

SONATYPE_CONNECT_TIMEOUT_SECONDS=300
android.useAndroidX=true
kotlin.code.style=official
kotlin.incremental.useClasspathSnapshot=true
kotlin.native.enableKlibsCrossCompilation=true
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8 -XX:+UseParallelGC
org.jetbrains.dokka.experimental.gradle.pluginMode.noWarn=true
org.jetbrains.dokka.experimental.gradle.pluginMode=V2Enabled
